#!/usr/bin/env python3
"""
改进SST+PCs+ERA5 ConvLSTM模型的建议方案实现
基于消融实验结果，提出多种改进策略

作者: AI Assistant
日期: 2025-07-02
"""

import torch
import torch.nn as nn
import numpy as np
import torch.nn.functional as F
from eof_convlstm_model import ConvLSTM

class ImprovedSSTWithPCsAndERA5ConvLSTMModel(nn.Module):
    """
    改进的SST+PCs+ERA5 ConvLSTM模型
    基于消融实验结果的多项改进策略
    """
    
    def __init__(self, num_pcs=10, seq_len=14, pred_len=1, hidden_dim=64, 
                 kernel_size=(3, 3), num_layers=2, dropout=0.1,
                 improvement_strategy="selective_pcs"):
        super(ImprovedSSTWithPCsAndERA5ConvLSTMModel, self).__init__()
        
        self.num_pcs = num_pcs
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dim = hidden_dim
        self.improvement_strategy = improvement_strategy
        
        # 根据改进策略调整输入维度
        if improvement_strategy == "selective_pcs":
            # 策略1: 只使用前5个最重要的PCs
            self.selected_pcs = 5
            input_dim = 1 + self.selected_pcs + 4  # SST + 5PCs + ERA5
        elif improvement_strategy == "weighted_fusion":
            # 策略2: 加权融合PCs和ERA5
            input_dim = 1 + num_pcs + 4
        elif improvement_strategy == "hierarchical_fusion":
            # 策略3: 分层融合
            input_dim = 1 + num_pcs + 4
        else:
            input_dim = 1 + num_pcs + 4
        
        self.input_dim = input_dim
        
        # PCs特征选择和处理
        if improvement_strategy == "selective_pcs":
            self.pc_selector = nn.Linear(num_pcs, self.selected_pcs)
        
        # 分组特征处理
        if improvement_strategy == "hierarchical_fusion":
            # SST分支
            self.sst_processor = nn.Sequential(
                nn.Conv2d(1, 16, kernel_size=3, padding=1),
                nn.BatchNorm2d(16),
                nn.ReLU(inplace=True)
            )
            
            # PCs分支
            self.pcs_processor = nn.Sequential(
                nn.Conv2d(num_pcs, 16, kernel_size=1, padding=0),
                nn.BatchNorm2d(16),
                nn.ReLU(inplace=True)
            )
            
            # ERA5分支
            self.era5_processor = nn.Sequential(
                nn.Conv2d(4, 16, kernel_size=3, padding=1),
                nn.BatchNorm2d(16),
                nn.ReLU(inplace=True)
            )
            
            # 融合层
            self.feature_fusion = nn.Sequential(
                nn.Conv2d(48, 32, kernel_size=3, padding=1),
                nn.BatchNorm2d(32),
                nn.ReLU(inplace=True),
                nn.Conv2d(32, input_dim, kernel_size=1, padding=0)
            )
        else:
            # 标准特征融合
            self.feature_fusion = nn.Sequential(
                nn.Conv2d(input_dim, input_dim, kernel_size=1, padding=0),
                nn.BatchNorm2d(input_dim),
                nn.ReLU(inplace=True),
                nn.Dropout2d(dropout)
            )
        
        # 加权注意力机制
        if improvement_strategy == "weighted_fusion":
            # 为不同特征组分配不同权重
            self.feature_weights = nn.Parameter(torch.ones(3))  # SST, PCs, ERA5
            self.feature_weights.data = torch.tensor([1.0, 0.5, 1.0])  # 降低PCs权重
        
        # ConvLSTM层
        self.convlstm = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=kernel_size,
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # 改进的输出投影层
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim // 2, hidden_dim // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 4, 1, kernel_size=1, padding=0)
        )
        
        # 残差连接
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1, padding=0)
        
        # 改进的注意力机制
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(input_dim, max(input_dim // 4, 1), kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(max(input_dim // 4, 1), input_dim, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 空间注意力
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(input_dim, 1, kernel_size=7, padding=3),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # x: [batch_size, seq_len, 1+num_pcs+4, lat, lon]
        batch_size, seq_len, channels, lat, lon = x.size()
        
        processed_sequence = []
        
        for t in range(seq_len):
            x_t = x[:, t]  # [batch_size, channels, lat, lon]
            
            if self.improvement_strategy == "selective_pcs":
                # 策略1: 选择性PCs使用
                sst_t = x_t[:, 0:1, :, :]  # SST
                pcs_t = x_t[:, 1:1+self.num_pcs, :, :]  # PCs
                era5_t = x_t[:, 1+self.num_pcs:, :, :]  # ERA5
                
                # 选择重要的PCs (通过学习的线性变换)
                pcs_spatial_mean = torch.mean(pcs_t, dim=(2, 3))  # [batch_size, num_pcs]
                selected_pcs_weights = self.pc_selector(pcs_spatial_mean)  # [batch_size, selected_pcs]
                
                # 重新构造PCs空间表示
                selected_pcs_spatial = selected_pcs_weights[:, :, None, None].expand(-1, -1, lat, lon)
                
                # 组合特征
                x_t_processed = torch.cat([sst_t, selected_pcs_spatial, era5_t], dim=1)
                
            elif self.improvement_strategy == "weighted_fusion":
                # 策略2: 加权融合
                sst_t = x_t[:, 0:1, :, :] * self.feature_weights[0]
                pcs_t = x_t[:, 1:1+self.num_pcs, :, :] * self.feature_weights[1]
                era5_t = x_t[:, 1+self.num_pcs:, :, :] * self.feature_weights[2]
                
                x_t_processed = torch.cat([sst_t, pcs_t, era5_t], dim=1)
                
            elif self.improvement_strategy == "hierarchical_fusion":
                # 策略3: 分层融合
                sst_t = x_t[:, 0:1, :, :]
                pcs_t = x_t[:, 1:1+self.num_pcs, :, :]
                era5_t = x_t[:, 1+self.num_pcs:, :, :]
                
                # 分别处理不同特征组
                sst_features = self.sst_processor(sst_t)
                pcs_features = self.pcs_processor(pcs_t)
                era5_features = self.era5_processor(era5_t)
                
                # 拼接并融合
                combined_features = torch.cat([sst_features, pcs_features, era5_features], dim=1)
                x_t_processed = self.feature_fusion(combined_features)
                
            else:
                x_t_processed = x_t
            
            # 通道注意力
            channel_att = self.channel_attention(x_t_processed)
            x_t_channel = x_t_processed * channel_att
            
            # 空间注意力
            spatial_att = self.spatial_attention(x_t_processed)
            x_t_spatial = x_t_channel * spatial_att
            
            # 特征融合
            if self.improvement_strategy != "hierarchical_fusion":
                x_t_fused = self.feature_fusion(x_t_spatial)
            else:
                x_t_fused = x_t_spatial
                
            processed_sequence.append(x_t_fused)
        
        # 重新组合时间序列
        processed_x = torch.stack(processed_sequence, dim=1)
        
        # ConvLSTM forward
        layer_output_list, last_state_list = self.convlstm(processed_x)
        convlstm_output = layer_output_list[0]
        
        # 使用最后一个时间步进行预测
        last_output = convlstm_output[:, -1]
        
        # 输出投影
        predicted_sst = self.output_projection(last_output)
        
        # 残差连接
        last_sst = x[:, -1, 0:1, :, :]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst
        
        # Reshape for pred_len=1
        output_sst = output_sst.squeeze(1)
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)
        
        return output_sst


class AdaptivePCsSelectionModel(nn.Module):
    """
    自适应PCs选择模型
    根据当前输入动态选择最相关的PCs
    """
    
    def __init__(self, num_pcs=10, seq_len=14, pred_len=1, hidden_dim=64):
        super(AdaptivePCsSelectionModel, self).__init__()
        
        self.num_pcs = num_pcs
        self.seq_len = seq_len
        
        # PCs重要性评估网络
        self.pc_importance_net = nn.Sequential(
            nn.Linear(num_pcs, num_pcs * 2),
            nn.ReLU(),
            nn.Linear(num_pcs * 2, num_pcs),
            nn.Sigmoid()
        )
        
        # 基础ConvLSTM模型 (类似消融模型)
        self.base_model = nn.Sequential(
            # 这里可以使用消融模型的架构
        )
        
    def forward(self, x):
        # x: [batch_size, seq_len, 1+num_pcs+4, lat, lon]
        batch_size, seq_len, channels, lat, lon = x.size()
        
        # 提取PCs的时空平均作为重要性评估输入
        pcs_data = x[:, :, 1:1+self.num_pcs, :, :]  # [batch_size, seq_len, num_pcs, lat, lon]
        pcs_temporal_spatial_mean = torch.mean(pcs_data, dim=(1, 3, 4))  # [batch_size, num_pcs]
        
        # 计算PCs重要性权重
        pc_weights = self.pc_importance_net(pcs_temporal_spatial_mean)  # [batch_size, num_pcs]
        
        # 应用权重到PCs
        weighted_pcs = pcs_data * pc_weights[:, None, :, None, None]
        
        # 重新组合输入
        sst_data = x[:, :, 0:1, :, :]
        era5_data = x[:, :, 1+self.num_pcs:, :, :]
        
        weighted_input = torch.cat([sst_data, weighted_pcs, era5_data], dim=2)
        
        # 使用基础模型进行预测
        return self.base_model(weighted_input)


# 训练改进模型的示例代码
def train_improved_model():
    """训练改进模型的示例"""
    
    # 创建不同改进策略的模型
    strategies = ["selective_pcs", "weighted_fusion", "hierarchical_fusion"]
    
    for strategy in strategies:
        print(f"\n训练改进策略: {strategy}")
        
        model = ImprovedSSTWithPCsAndERA5ConvLSTMModel(
            num_pcs=10,
            seq_len=14,
            pred_len=1,
            hidden_dim=64,
            improvement_strategy=strategy
        )
        
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 这里可以添加训练循环
        # train_loop(model, strategy)

if __name__ == "__main__":
    train_improved_model()
