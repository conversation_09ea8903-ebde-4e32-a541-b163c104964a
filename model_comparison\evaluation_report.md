
# 模型性能比较报告

## 评估指标说明
- **PCs MSE/MAE**: 主成分时间系数预测的均方误差和平均绝对误差
- **SST RMSE/MAE**: 海表温度场预测/重构的均方根误差和平均绝对误差（摄氏度）

## 性能比较结果

                                     模型    PCs MSE  PCs MAE  SST RMSE (°C)  SST MAE (°C)
                            Transformer 122.600299 7.194269       0.307795      0.241535
                     EOF-ConvLSTM-Basic        NaN      NaN            NaN           NaN
                   EOF-ConvLSTM-Spatial        NaN      NaN            NaN           NaN
                 SST+PCs-ConvLSTM-Basic        NaN      NaN       0.263555      0.201426
            SST+PCs-ConvLSTM-MultiScale        NaN      NaN            NaN           NaN
            SST+PCs+ERA5-ConvLSTM-Basic        NaN      NaN       0.102037      0.077562
       SST+PCs+ERA5-ConvLSTM-MultiScale        NaN      NaN            NaN           NaN
     SST+ERA5-ConvLSTM-Basic (Ablation)        NaN      NaN       0.101881      0.077197
SST+ERA5-ConvLSTM-MultiScale (Ablation)        NaN      NaN            NaN           NaN

## 模型分析

### Transformer模型
- 基于注意力机制的序列到序列预测
- 直接在主成分空间进行预测
- 优势：能够捕捉长期依赖关系
- 方法：PCs预测 → EOF重构 → SST场

### EOF-ConvLSTM-Basic模型
- 将主成分映射到空间表示，使用ConvLSTM进行时空建模
- 优势：结合了空间卷积和时间记忆能力
- 特点：使用全连接层进行空间映射
- 方法：PCs预测 → EOF重构 → SST场

### EOF-ConvLSTM-Spatial模型
- 直接使用EOF空间模态进行空间表示
- 优势：保持了EOF的物理意义
- 特点：更直接的空间-时间建模方式
- 方法：PCs预测 → EOF重构 → SST场

### SST+PCs-ConvLSTM-Basic模型
- 将PCs作为额外特征与SST一起输入ConvLSTM
- 优势：端到端训练，避免误差累积
- 特点：直接预测SST，无需重构步骤
- 方法：SST+PCs → ConvLSTM → 直接SST预测

### SST+PCs-ConvLSTM-MultiScale模型
- 多尺度ConvLSTM架构，同时处理细粒度和粗粒度特征
- 优势：更强的特征表达能力
- 特点：并行多尺度处理，特征融合
- 方法：SST+PCs → 多尺度ConvLSTM → 直接SST预测

## 建议
1. **精度优先**: 选择RMSE最低的模型
2. **效率优先**: 选择直接SST预测模型（避免重构步骤）
3. **稳定性**: 考虑多个指标的综合表现
4. **集成方法**: 可以尝试集成多个模型的预测结果

## 生成时间
2025-07-04 14:15:29
