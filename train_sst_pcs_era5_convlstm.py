#!/usr/bin/env python3
"""
SST+PCs+ERA5 ConvLSTM模型训练脚本
使用SST场、PCs特征和ERA5大气变量作为输入，直接预测SST场

作者: AI Assistant
日期: 2025-07-02
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import os
import time
from sklearn.metrics import mean_squared_error, mean_absolute_error
from tqdm import tqdm

# 导入模型
from eof_convlstm_model import SSTWithPCsAndERA5ConvLSTMModel, MultiScaleSSTWithPCsAndERA5ConvLSTMModel

print("开始SST+PCs+ERA5 ConvLSTM模型训练...")

# 创建结果文件夹
os.makedirs('sst_pcs_era5_results', exist_ok=True)
os.makedirs('sst_pcs_era5_figures', exist_ok=True)

# 设置随机种子确保可重复性
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载预处理后的数据
print("加载预处理后的数据...")

# SST数据
sst_train = np.load('sst_pcs_era5_data/sst_train_norm.npy')
sst_val = np.load('sst_pcs_era5_data/sst_val_norm.npy')
sst_test = np.load('sst_pcs_era5_data/sst_test_norm.npy')

# ERA5数据
era5_vars = ['u10', 'v10', 't2m', 'msl']
era5_train = {}
era5_val = {}
era5_test = {}

for var in era5_vars:
    era5_train[var] = np.load(f'sst_pcs_era5_data/{var}_train_norm.npy')
    era5_val[var] = np.load(f'sst_pcs_era5_data/{var}_val_norm.npy')
    era5_test[var] = np.load(f'sst_pcs_era5_data/{var}_test_norm.npy')

# PCs数据
pcs_train = np.load('sst_pcs_era5_data/pcs_train_scaled.npy')
pcs_val = np.load('sst_pcs_era5_data/pcs_val_scaled.npy')
pcs_test = np.load('sst_pcs_era5_data/pcs_test_scaled.npy')

print(f"SST训练集形状: {sst_train.shape}")
print(f"PCs训练集形状: {pcs_train.shape}")
for var in era5_vars:
    print(f"{var}训练集形状: {era5_train[var].shape}")

# 获取数据维度
n_time_train, n_lat, n_lon = sst_train.shape
num_pcs = pcs_train.shape[1]

print(f"数据维度: 时间={n_time_train}, 纬度={n_lat}, 经度={n_lon}, PCs={num_pcs}")

class SSTWithPCsAndERA5Dataset(Dataset):
    """SST+PCs+ERA5数据集类"""
    
    def __init__(self, sst_data, pcs_data, era5_data, seq_len=14, pred_len=1):
        self.sst_data = sst_data
        self.pcs_data = pcs_data
        self.era5_data = era5_data
        self.seq_len = seq_len
        self.pred_len = pred_len
        
        # 计算有效样本数量
        self.n_samples = len(sst_data) - seq_len - pred_len + 1
        
    def __len__(self):
        return self.n_samples
    
    def __getitem__(self, idx):
        # 输入序列
        input_sst = self.sst_data[idx:idx+self.seq_len]  # [seq_len, lat, lon]
        input_pcs = self.pcs_data[idx:idx+self.seq_len]  # [seq_len, num_pcs]
        
        # ERA5输入序列
        input_era5 = []
        for var in ['u10', 'v10', 't2m', 'msl']:
            input_era5.append(self.era5_data[var][idx:idx+self.seq_len])
        input_era5 = np.stack(input_era5, axis=1)  # [seq_len, 4, lat, lon]
        
        # 将PCs广播到空间维度
        pcs_spatial = np.broadcast_to(
            input_pcs[:, :, np.newaxis, np.newaxis], 
            (self.seq_len, input_pcs.shape[1], input_sst.shape[1], input_sst.shape[2])
        )  # [seq_len, num_pcs, lat, lon]
        
        # 组合所有输入特征: [seq_len, 1+num_pcs+4, lat, lon]
        combined_input = np.concatenate([
            input_sst[:, np.newaxis, :, :],  # SST: [seq_len, 1, lat, lon]
            pcs_spatial,                     # PCs: [seq_len, num_pcs, lat, lon]
            input_era5                       # ERA5: [seq_len, 4, lat, lon]
        ], axis=1)
        
        # 目标SST
        target_sst = self.sst_data[idx+self.seq_len:idx+self.seq_len+self.pred_len]
        
        return torch.FloatTensor(combined_input), torch.FloatTensor(target_sst)

# 超参数设置
seq_len = 14
pred_len = 1
batch_size = 8  # 减小批次大小以适应更大的输入
hidden_dim = 64
num_layers = 2
learning_rate = 0.001
weight_decay = 1e-4
num_epochs = 80
patience = 15
clip_grad = 1.0

print(f"超参数设置:")
print(f"  序列长度: {seq_len}")
print(f"  预测长度: {pred_len}")
print(f"  批次大小: {batch_size}")
print(f"  隐藏维度: {hidden_dim}")
print(f"  学习率: {learning_rate}")

# 创建数据集和数据加载器
print("创建数据集和数据加载器...")

train_dataset = SSTWithPCsAndERA5Dataset(sst_train, pcs_train, era5_train, seq_len, pred_len)
val_dataset = SSTWithPCsAndERA5Dataset(sst_val, pcs_val, era5_val, seq_len, pred_len)
test_dataset = SSTWithPCsAndERA5Dataset(sst_test, pcs_test, era5_test, seq_len, pred_len)

train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

print(f"数据集大小:")
print(f"  训练集: {len(train_dataset)} 样本")
print(f"  验证集: {len(val_dataset)} 样本")
print(f"  测试集: {len(test_dataset)} 样本")

# 训练函数
def train_model(model, model_name):
    print(f"\n开始训练 {model_name} 模型...")
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    criterion = nn.MSELoss()
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    print(f"开始训练，共 {num_epochs} 个epoch...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        
        for batch_idx, (inputs, targets) in enumerate(train_pbar):
            inputs, targets = inputs.to(device), targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)
            
            optimizer.step()
            train_loss += loss.item()
            
            train_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for inputs, targets in val_pbar:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
                val_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_val_loss = val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  训练损失: {avg_train_loss:.6f}')
        print(f'  验证损失: {avg_val_loss:.6f}')
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), f'sst_pcs_era5_results/best_{model_name}.pth')
            print(f'  保存最佳模型 (验证损失: {best_val_loss:.6f})')
        else:
            patience_counter += 1
            print(f'  早停计数: {patience_counter}/{patience}')
        
        if patience_counter >= patience:
            print(f'早停触发，停止训练')
            break
    
    return train_losses, val_losses

# 选择要训练的模型类型
model_type = "multiscale"  # 可选: "basic", "multiscale", "both"

if model_type in ["basic", "both"]:
    print("\n" + "="*60)
    print("训练基础SST+PCs+ERA5 ConvLSTM模型")
    print("="*60)
    
    # 创建基础模型
    basic_model = SSTWithPCsAndERA5ConvLSTMModel(
        num_pcs=num_pcs,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=hidden_dim,
        num_layers=num_layers
    ).to(device)
    
    print(f"基础模型参数数量: {sum(p.numel() for p in basic_model.parameters()):,}")
    
    # 训练基础模型
    basic_train_losses, basic_val_losses = train_model(basic_model, "sst_pcs_era5_basic")

if model_type in ["multiscale", "both"]:
    print("\n" + "="*60)
    print("训练多尺度SST+PCs+ERA5 ConvLSTM模型")
    print("="*60)
    
    # 创建多尺度模型
    multiscale_model = MultiScaleSSTWithPCsAndERA5ConvLSTMModel(
        num_pcs=num_pcs,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=hidden_dim,
        num_layers=num_layers
    ).to(device)
    
    print(f"多尺度模型参数数量: {sum(p.numel() for p in multiscale_model.parameters()):,}")
    
    # 训练多尺度模型
    multiscale_train_losses, multiscale_val_losses = train_model(multiscale_model, "sst_pcs_era5_multiscale")

# 测试模型性能
def test_model(model, model_name):
    print(f"\n测试 {model_name} 模型性能...")

    # 加载最佳模型
    model.load_state_dict(torch.load(f'sst_pcs_era5_results/best_{model_name}.pth'))
    model.eval()

    test_predictions = []
    test_targets = []

    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc=f'测试 {model_name}')
        for inputs, targets in test_pbar:
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)

            test_predictions.append(outputs.cpu().numpy())
            test_targets.append(targets.cpu().numpy())

    # 合并所有预测结果
    test_predictions = np.concatenate(test_predictions, axis=0)
    test_targets = np.concatenate(test_targets, axis=0)

    # 计算评估指标
    mse = mean_squared_error(test_targets.flatten(), test_predictions.flatten())
    mae = mean_absolute_error(test_targets.flatten(), test_predictions.flatten())
    rmse = np.sqrt(mse)

    print(f"{model_name} 测试结果:")
    print(f"  MSE: {mse:.6f}")
    print(f"  MAE: {mae:.6f}")
    print(f"  RMSE: {rmse:.6f}")

    # 保存测试结果
    np.save(f'sst_pcs_era5_results/test_predictions_{model_name}.npy', test_predictions)
    np.save(f'sst_pcs_era5_results/test_targets_{model_name}.npy', test_targets)

    return test_predictions, test_targets, {'mse': mse, 'mae': mae, 'rmse': rmse}

# 绘制训练曲线
def plot_training_curves(train_losses, val_losses, model_name):
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='训练损失', color='blue')
    plt.plot(val_losses, label='验证损失', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.title(f'{model_name} 训练曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'sst_pcs_era5_figures/training_curves_{model_name}.png', dpi=300, bbox_inches='tight')
    plt.close()

# 测试训练好的模型
if model_type in ["basic", "both"]:
    basic_predictions, basic_targets, basic_metrics = test_model(basic_model, "sst_pcs_era5_basic")
    plot_training_curves(basic_train_losses, basic_val_losses, "sst_pcs_era5_basic")

if model_type in ["multiscale", "both"]:
    multiscale_predictions, multiscale_targets, multiscale_metrics = test_model(multiscale_model, "sst_pcs_era5_multiscale")
    plot_training_curves(multiscale_train_losses, multiscale_val_losses, "sst_pcs_era5_multiscale")

print("\nSST+PCs+ERA5 ConvLSTM模型训练完成！")
