import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # Set font that supports Chinese
rcParams['axes.unicode_minus'] = False    # Fix negative sign display
import os
import time
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings
from tqdm import tqdm

# 导入统一的可视化配置
from visualization_config import (
    SST_COLORMAP, SST_VMIN, SST_VMAX,
    ERROR_COLORMAP, ERROR_VMIN, ERROR_VMAX,
    RMSE_COLORMAP, RMSE_VMIN, RMSE_VMAX,
    TIMESERIES_COLORS, FIGURE_SIZES, FONT_SIZES, DPI,
    get_sample_date_mapping, format_date_for_title,
    get_adaptive_sst_range, get_adaptive_error_range
)


# 忽略cartopy下载警告
warnings.filterwarnings('ignore', category=UserWarning, module='cartopy')

print("开始可视化SST+PCs ConvLSTM预测结果...")

# 创建结果文件夹
os.makedirs('sst_pcs_convlstm_visualization', exist_ok=True)

# 选择模型类型
model_type = "basic"  # 可选: "basic" 或 "multiscale"

# 加载SST+PCs ConvLSTM预测结果
print("📂 加载SST+PCs ConvLSTM预测结果...")

data_files = [
    (f'sst_pcs_convlstm_results/test_predictions_{model_type}.npy', '预测结果'),
    (f'sst_pcs_convlstm_results/test_targets_{model_type}.npy', '真实值'),
    ('SST-V2.nc', 'SST原始数据')
]

with tqdm(data_files, desc='加载数据文件', ncols=100) as pbar:
    for filepath, desc in pbar:
        pbar.set_description(f'加载{desc}')
        if filepath.endswith('.npy'):
            if '预测结果' in desc:
                predicted_sst = np.load(filepath)
            else:
                true_sst = np.load(filepath)
        else:
            sst_data = xr.open_dataset(filepath)

n_lat = len(sst_data.latitude)
n_lon = len(sst_data.longitude)

# 确定测试集的时间范围
total_time = len(sst_data.time)
test_start_idx = int(total_time * 0.9)
test_times = sst_data.time.values[test_start_idx:]

print(f"\n📊 数据信息:")
print(f"  预测SST形状: {predicted_sst.shape}")
print(f"  真实SST形状: {true_sst.shape}")
print(f"  测试集时间点数: {len(test_times)}")
print(f"  纬度点数: {n_lat}, 经度点数: {n_lon}")

# 提取单步预测结果
if predicted_sst.ndim == 4:  # (samples, pred_len, lat, lon)
    sst_pred_celsius = predicted_sst[:, 0, :, :]  # 取第一个预测步
    sst_true_celsius = true_sst[:, 0, :, :]
else:  # (samples, lat, lon)
    sst_pred_celsius = predicted_sst
    sst_true_celsius = true_sst

# 计算误差
print("计算预测误差...")
sst_error = sst_pred_celsius - sst_true_celsius  # 形状: (samples, lat, lon)

# 计算整体RMSE和MAE（摄氏度）
rmse = np.sqrt(np.mean(sst_error ** 2))
mae = np.mean(np.abs(sst_error))
print(f"SST+PCs ConvLSTM ({model_type}) SST场预测的整体RMSE: {rmse:.4f}°C")
print(f"SST+PCs ConvLSTM ({model_type}) SST场预测的整体MAE: {mae:.4f}°C")

# 保存误差统计
error_stats = {
    'rmse': rmse,
    'mae': mae,
    'model_type': model_type
}
np.save(f'sst_pcs_convlstm_visualization/error_stats_{model_type}.npy', error_stats)

# 创建包含预测结果的NetCDF文件
print("创建包含预测结果的NetCDF文件...")
if len(test_times) > sst_pred_celsius.shape[0]:
    test_times = test_times[:sst_pred_celsius.shape[0]]

# 创建xarray数据集
ds_pred = xr.Dataset(
    data_vars={
        'sst_pred': (['time', 'latitude', 'longitude'], sst_pred_celsius),
        'sst_true': (['time', 'latitude', 'longitude'], sst_true_celsius),
        'sst_error': (['time', 'latitude', 'longitude'], sst_error)
    },
    coords={
        'time': test_times,
        'latitude': sst_data.latitude.values,
        'longitude': sst_data.longitude.values
    },
    attrs={
        'description': f'SST+PCs ConvLSTM ({model_type}) SST预测结果和误差',
        'units': '摄氏度',
        'created': time.ctime(),
        'model_type': model_type
    }
)

# 保存为NetCDF文件
ds_pred.to_netcdf(f'sst_pcs_convlstm_visualization/sst_prediction_results_{model_type}.nc')

# 可视化预测结果
print("可视化预测结果...")

# 设置地图属性
map_proj = ccrs.PlateCarree()

# 预先下载自然地球数据以避免下载警告
print("准备地图特征数据...")
coastlines = cfeature.NaturalEarthFeature('physical', 'coastline', '50m')
land = cfeature.NaturalEarthFeature('physical', 'land', '50m', 
                                   edgecolor='black', facecolor='lightgray', alpha=0.5)

# 定义并排可视化函数
def plot_sst_comparison(true_data, pred_data, error_data, day_str, filename):
    """绘制并排的真实SST、预测SST和误差场"""
    fig, axes = plt.subplots(1, 3, figsize=(24, 8), subplot_kw={'projection': map_proj})
    
    # 通过计算百分位数确定合适的颜色范围
    vmin_sst = np.percentile(true_data, 2)
    vmax_sst = np.percentile(true_data, 98)
    
    # 误差范围
    abs_max_error = max(abs(np.percentile(error_data, 2)), abs(np.percentile(error_data, 98)))
    vmin_err, vmax_err = -abs_max_error, abs_max_error
    
    # 1. 真实SST场
    axes[0].set_title(f'真实SST场 ({day_str})', fontsize=FONT_SIZES['title'])
    axes[0].coastlines(resolution='50m')
    axes[0].add_feature(land)
    axes[0].gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    im1 = axes[0].pcolormesh(
        sst_data.longitude, 
        sst_data.latitude, 
        true_data, 
        cmap=SST_COLORMAP,
        vmin=vmin_sst, 
        vmax=vmax_sst,
        transform=ccrs.PlateCarree()
    )
    cbar1 = fig.colorbar(im1, ax=axes[0], pad=0.01, extend='both')
    cbar1.set_label('温度 (°C)', fontsize=FONT_SIZES['label'])
    
    # 2. 预测SST场
    axes[1].set_title(f'SST+PCs ConvLSTM ({model_type}) 预测SST场 ({day_str})', fontsize=FONT_SIZES['title'])
    axes[1].coastlines(resolution='50m')
    axes[1].add_feature(land)
    axes[1].gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    im2 = axes[1].pcolormesh(
        sst_data.longitude, 
        sst_data.latitude, 
        pred_data, 
        cmap=SST_COLORMAP,
        vmin=vmin_sst, 
        vmax=vmax_sst,
        transform=ccrs.PlateCarree()
    )
    cbar2 = fig.colorbar(im2, ax=axes[1], pad=0.01, extend='both')
    cbar2.set_label('温度 (°C)', fontsize=FONT_SIZES['label'])
    
    # 3. 误差场
    axes[2].set_title(f'预测误差 ({day_str})', fontsize=FONT_SIZES['title'])
    axes[2].coastlines(resolution='50m')
    axes[2].add_feature(land)
    axes[2].gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    im3 = axes[2].pcolormesh(
        sst_data.longitude, 
        sst_data.latitude, 
        error_data, 
        cmap=ERROR_COLORMAP,
        vmin=vmin_err, 
        vmax=vmax_err,
        transform=ccrs.PlateCarree()
    )
    cbar3 = fig.colorbar(im3, ax=axes[2], pad=0.01, extend='both')
    cbar3.set_label('温差 (°C)', fontsize=FONT_SIZES['label'])
    
    plt.tight_layout()
    plt.savefig(filename, dpi=DPI, bbox_inches='tight')
    plt.close()

# 可视化示例时间点
print("\n🖼️ 生成时间点对比图...")
sample_days = [0, len(sst_pred_celsius)//4, len(sst_pred_celsius)//2, 3*len(sst_pred_celsius)//4, -1]

with tqdm(sample_days, desc='生成对比图', ncols=100) as pbar:
    for i, day_idx in enumerate(pbar):
        day_idx = day_idx if day_idx >= 0 else len(sst_pred_celsius) + day_idx

        if day_idx < len(test_times):
            day_str = str(test_times[day_idx]).split('T')[0]

            pbar.set_description(f'生成第{i+1}/5个样本: {day_str}')
            plot_sst_comparison(
                sst_true_celsius[day_idx],
                sst_pred_celsius[day_idx],
                sst_error[day_idx],
                day_str,
                f'sst_pcs_convlstm_visualization/sst_comparison_day{day_idx}_{model_type}.png'
            )

# 计算时间平均场
print("\n📊 计算时间平均场和误差统计...")
with tqdm(total=4, desc='计算统计量', ncols=100) as pbar:
    pbar.set_description('计算时间平均真实值')
    mean_true_sst = np.mean(sst_true_celsius, axis=0)
    pbar.update(1)

    pbar.set_description('计算时间平均预测值')
    mean_pred_sst = np.mean(sst_pred_celsius, axis=0)
    pbar.update(1)

    pbar.set_description('计算时间平均误差')
    mean_error = np.mean(sst_error, axis=0)
    pbar.update(1)

    pbar.set_description('计算RMSE分布')
    rmse_map = np.sqrt(np.mean(sst_error**2, axis=0))
    pbar.update(1)

# 生成各种可视化图表
visualization_tasks = [
    ('时间平均对比图', 'time_averaged_comparison'),
    ('RMSE分布图', 'rmse_map'),
    ('误差时间序列', 'error_time_series'),
    ('空间平均时间序列', 'spatial_mean_timeseries')
]

print("\n🎨 生成可视化图表...")
with tqdm(visualization_tasks, desc='生成图表', ncols=100) as pbar:
    for task_name, filename_prefix in pbar:
        pbar.set_description(f'生成{task_name}')

        if task_name == '时间平均对比图':
            plot_sst_comparison(
                mean_true_sst,
                mean_pred_sst,
                mean_error,
                '时间平均',
                f'sst_pcs_convlstm_visualization/time_averaged_comparison_{model_type}.png'
            )

        elif task_name == 'RMSE分布图':
            plt.figure(figsize=FIGURE_SIZES['single_map'])
            ax = plt.axes(projection=map_proj)
            ax.coastlines(resolution='50m')
            ax.add_feature(land)
            ax.gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')

            vmax_rmse = np.percentile(rmse_map, 98)
            im = ax.pcolormesh(
                sst_data.longitude,
                sst_data.latitude,
                rmse_map,
                cmap=RMSE_COLORMAP,
                vmin=0,
                vmax=vmax_rmse,
                transform=ccrs.PlateCarree()
            )
            cbar = plt.colorbar(im, ax=ax, pad=0.01, extend='max')
            cbar.set_label('RMSE (°C)', fontsize=FONT_SIZES['label'])

            # 添加统计信息
            avg_rmse = np.mean(rmse_map)
            max_rmse = np.max(rmse_map)
            plt.text(0.02, 0.98, f'平均RMSE: {avg_rmse:.4f}°C\n最大RMSE: {max_rmse:.4f}°C',
                    transform=ax.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            plt.title(f'SST+PCs ConvLSTM ({model_type}) 时间平均RMSE分布图', fontsize=FONT_SIZES['title'])
            plt.savefig(f'sst_pcs_convlstm_visualization/rmse_map_{model_type}.png', dpi=DPI, bbox_inches='tight')
            plt.close()

        elif task_name == '误差时间序列':
            plt.figure(figsize=FIGURE_SIZES['time_series'])
            time_steps = range(len(sst_error))
            mean_abs_error = np.mean(np.abs(sst_error), axis=(1, 2))
            rmse_time = np.sqrt(np.mean(sst_error**2, axis=(1, 2)))

            plt.plot(time_steps, mean_abs_error, label='平均绝对误差', color=TIMESERIES_COLORS['true'], linewidth=2)
            plt.plot(time_steps, rmse_time, label='RMSE', color=TIMESERIES_COLORS['predicted'], linewidth=2)
            plt.xlabel('时间步', fontsize=FONT_SIZES['label'])
            plt.ylabel('误差 (°C)', fontsize=FONT_SIZES['label'])
            plt.title(f'SST+PCs ConvLSTM ({model_type}) SST预测误差随时间变化', fontsize=FONT_SIZES['title'])
            plt.legend(fontsize=FONT_SIZES['label'])
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(f'sst_pcs_convlstm_visualization/error_time_series_{model_type}.png', dpi=DPI, bbox_inches='tight')
            plt.close()

        elif task_name == '空间平均时间序列':
            plt.figure(figsize=(15, 8))
            time_steps = range(len(sst_pred_celsius))
            true_spatial_mean = np.mean(sst_true_celsius, axis=(1, 2))
            pred_spatial_mean = np.mean(sst_pred_celsius, axis=(1, 2))

            plt.plot(time_steps, true_spatial_mean, label='真实SST空间平均', color=TIMESERIES_COLORS['true'], linewidth=2)
            plt.plot(time_steps, pred_spatial_mean, label='预测SST空间平均', color=TIMESERIES_COLORS['predicted'], alpha=0.8, linewidth=2)

            # 计算相关系数
            correlation = np.corrcoef(true_spatial_mean, pred_spatial_mean)[0, 1]

            plt.xlabel('时间步', fontsize=FONT_SIZES['label'])
            plt.ylabel('SST (°C)', fontsize=FONT_SIZES['label'])
            plt.title(f'SST+PCs ConvLSTM ({model_type}) 空间平均SST时间序列对比\n相关系数: {correlation:.4f}', fontsize=FONT_SIZES['title'])
            plt.legend(fontsize=FONT_SIZES['label'])
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(f'sst_pcs_convlstm_visualization/spatial_mean_timeseries_{model_type}.png', dpi=DPI, bbox_inches='tight')
            plt.close()

print(f"\n🎉 SST+PCs ConvLSTM ({model_type}) 可视化完成！")
print(f"📁 图表保存在: sst_pcs_convlstm_visualization/")
print(f"📊 生成了 {len(visualization_tasks) + len(sample_days)} 个图表文件")

# 关闭文件
plt.close('all')
sst_data.close()
