#!/usr/bin/env python3
"""
调试数据维度问题
检查EOF分解结果和数据维度匹配

作者: AI Assistant
日期: 2025-07-07
"""

import numpy as np
import xarray as xr
import os

print("🔍 调试数据维度问题...")

# 获取项目根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)

print(f"项目根目录: {project_root}")

# 检查EOF分解结果
print("\n📊 检查EOF分解结果...")
try:
    EOFs_k = np.load(os.path.join(project_root, 'results', 'EOFs_k.npy'))
    PCs_train = np.load(os.path.join(project_root, 'results', 'PCs_train.npy'))
    PCs_val = np.load(os.path.join(project_root, 'results', 'PCs_val.npy'))
    PCs_test = np.load(os.path.join(project_root, 'results', 'PCs_test.npy'))
    
    print(f"EOF模态形状: {EOFs_k.shape}")
    print(f"训练集PCs形状: {PCs_train.shape}")
    print(f"验证集PCs形状: {PCs_val.shape}")
    print(f"测试集PCs形状: {PCs_test.shape}")
    
except Exception as e:
    print(f"❌ 加载EOF结果失败: {e}")
    exit(1)

# 检查原始数据维度
print("\n🌊 检查原始数据维度...")
try:
    sst_data = xr.open_dataset(os.path.join(project_root, 'SST-V2.nc'))
    era5_data = xr.open_dataset(os.path.join(project_root, 'data_ERA5.nc'))
    
    print(f"SST数据形状: {sst_data.analysed_sst.shape}")
    print(f"SST纬度数: {len(sst_data.latitude)}")
    print(f"SST经度数: {len(sst_data.longitude)}")
    print(f"SST时间数: {len(sst_data.time)}")
    
    print(f"ERA5数据形状: {era5_data.t2m.shape}")
    print(f"ERA5纬度数: {len(era5_data.latitude)}")
    print(f"ERA5经度数: {len(era5_data.longitude)}")
    print(f"ERA5时间数: {len(era5_data.time)}")
    
    # 计算期望的空间点数
    n_lat = len(sst_data.latitude)
    n_lon = len(sst_data.longitude)
    n_vars = 2  # SST + T2m
    expected_spatial_points = n_vars * n_lat * n_lon
    actual_spatial_points = EOFs_k.shape[0]
    
    print(f"\n🔢 维度分析:")
    print(f"SST空间维度: {n_lat} x {n_lon} = {n_lat * n_lon}")
    print(f"变量数: {n_vars}")
    print(f"期望空间点数: {expected_spatial_points}")
    print(f"实际EOF空间点数: {actual_spatial_points}")
    print(f"维度匹配: {'✅' if expected_spatial_points == actual_spatial_points else '❌'}")
    
    sst_data.close()
    era5_data.close()
    
except Exception as e:
    print(f"❌ 加载原始数据失败: {e}")

# 检查数据集划分
print("\n📊 检查数据集划分...")
try:
    # 加载SST数据检查划分
    sst_data = xr.open_dataset(os.path.join(project_root, 'SST-V2.nc'))
    n_time_total = len(sst_data.time)
    
    # 数据集划分比例 (应该与EOF分解保持一致)
    train_end = int(n_time_total * 0.7)
    val_end = int(n_time_total * 0.9)
    
    train_size = train_end
    val_size = val_end - train_end
    test_size = n_time_total - val_end
    
    print(f"总时间点数: {n_time_total}")
    print(f"训练集大小: {train_size}")
    print(f"验证集大小: {val_size}")
    print(f"测试集大小: {test_size}")
    
    print(f"\nPCs数据集大小:")
    print(f"训练集PCs: {PCs_train.shape[1]}")
    print(f"验证集PCs: {PCs_val.shape[1]}")
    print(f"测试集PCs: {PCs_test.shape[1]}")
    
    # 检查是否匹配
    pcs_train_match = PCs_train.shape[1] == train_size
    pcs_val_match = PCs_val.shape[1] == val_size
    pcs_test_match = PCs_test.shape[1] == test_size
    
    print(f"\n数据集大小匹配:")
    print(f"训练集: {'✅' if pcs_train_match else '❌'}")
    print(f"验证集: {'✅' if pcs_val_match else '❌'}")
    print(f"测试集: {'✅' if pcs_test_match else '❌'}")
    
    sst_data.close()
    
except Exception as e:
    print(f"❌ 检查数据集划分失败: {e}")

# 模拟EOF重塑过程
print("\n🔧 模拟EOF重塑过程...")
try:
    # 获取实际维度
    sst_data = xr.open_dataset(os.path.join(project_root, 'SST-V2.nc'))
    n_lat = len(sst_data.latitude)
    n_lon = len(sst_data.longitude)
    n_vars = 2
    k = EOFs_k.shape[1]
    
    print(f"尝试重塑EOF模态:")
    print(f"原始形状: {EOFs_k.shape}")
    print(f"目标形状: ({n_vars}, {n_lat}, {n_lon}, {k})")
    
    # 尝试重塑
    try:
        EOFs_spatial = EOFs_k.reshape(n_vars, n_lat, n_lon, k)
        print(f"✅ 重塑成功: {EOFs_spatial.shape}")
        
        EOFs_sst = EOFs_spatial[0]  # SST的EOF模态
        print(f"SST EOF模态形状: {EOFs_sst.shape}")
        
        # 模拟创建加权特征
        time_steps = PCs_train.shape[1]
        print(f"训练集时间步数: {time_steps}")
        
        # 计算期望的加权特征大小
        expected_size = time_steps * k * n_lat * n_lon
        print(f"期望加权特征大小: {expected_size}")
        
        # 实际创建一小部分来测试
        test_time_steps = min(10, time_steps)
        test_weighted_features = np.zeros((test_time_steps, k, n_lat, n_lon))
        
        for t in range(test_time_steps):
            for mode_idx in range(k):
                pc_value = PCs_train[mode_idx, t]
                test_weighted_features[t, mode_idx] = pc_value * EOFs_sst[:, :, mode_idx]
        
        print(f"✅ 测试加权特征创建成功: {test_weighted_features.shape}")
        
    except Exception as e:
        print(f"❌ EOF重塑失败: {e}")
    
    sst_data.close()
    
except Exception as e:
    print(f"❌ 模拟重塑过程失败: {e}")

# 检查已生成的加权特征文件
print("\n📁 检查已生成的文件...")
weighted_files = [
    'eof_weighted_data/train_eof_weighted_features.npy',
    'eof_weighted_data/val_eof_weighted_features.npy',
    'eof_weighted_data/test_eof_weighted_features.npy'
]

for file_path in weighted_files:
    if os.path.exists(file_path):
        try:
            data = np.load(file_path)
            print(f"✅ {file_path}: {data.shape}")
            print(f"   数据大小: {data.size} 个元素")
        except Exception as e:
            print(f"❌ {file_path}: 加载失败 - {e}")
    else:
        print(f"⚠️  {file_path}: 文件不存在")

print("\n🎯 问题诊断完成！")
print("请检查上述输出，找出维度不匹配的原因。")
