# SST+PCs+ERA5 ConvLSTM 海表温度预测方案

## 概述

本方案是对现有SST预测方法的重要扩展，将**ERA5大气再分析数据**作为额外输入特征，与SST场和PCs特征一起输入ConvLSTM模型，实现更准确的海表温度直接预测。

## 核心创新

### 技术路线对比

**传统Transformer方案:**
```
PCs时间序列 → Transformer → 预测PCs → EOF重构 → SST场
```

**SST+PCs ConvLSTM方案:**
```
SST场 + PCs特征 → ConvLSTM → 直接预测SST场
```

**SST+PCs+ERA5 ConvLSTM方案 (本方案):**
```
SST场 + PCs特征 + ERA5大气变量 → ConvLSTM → 直接预测SST场
```

### 主要创新点

1. **多源数据融合**: 同时利用海洋(SST)、降维(PCs)和大气(ERA5)信息
2. **大气-海洋耦合**: 考虑大气变量对SST的直接影响
3. **端到端预测**: 避免中间重构步骤的误差累积
4. **多尺度建模**: 提供基础版和多尺度版本两种架构

## ERA5数据特征分析

### 变量说明
- **u10**: 10米高度东西向风速分量 (m/s)
- **v10**: 10米高度南北向风速分量 (m/s)  
- **t2m**: 2米高度气温 (K)
- **msl**: 海平面气压 (Pa)

### 与SST相关性分析
根据我们的分析结果：
- **t2m (2m温度)**: 0.9554 - 极强正相关
- **msl (海平面气压)**: -0.7927 - 强负相关
- **v10 (10m风速v分量)**: 0.6430 - 中等正相关
- **u10 (10m风速u分量)**: 0.6129 - 中等正相关

这些强相关性为将ERA5数据作为预测特征提供了科学依据。

## 模型架构

### 1. 基础SST+PCs+ERA5 ConvLSTM模型

```python
输入: [SST, PCs_spatial, u10, v10, t2m, msl] 
     → [batch_size, seq_len, 1+num_pcs+4, lat, lon]
  ↓
通道注意力机制: 对不同特征通道加权
  ↓
特征融合层: Conv2d + BatchNorm + ReLU + Dropout
  ↓
ConvLSTM: 时空序列建模
  ↓
输出投影: 多层卷积 → 单通道SST
  ↓
残差连接: 与输入最后时间步SST相加
  ↓
输出: 预测SST [batch_size, pred_len, lat, lon]
```

**特点:**
- 通道注意力机制自动学习不同特征的重要性
- 特征融合层优化多模态信息整合
- 残差连接保持时序连续性

### 2. 多尺度SST+PCs+ERA5 ConvLSTM模型

```python
输入: [SST, PCs_spatial, u10, v10, t2m, msl]
  ↓
并行ConvLSTM分支:
├── 细尺度ConvLSTM (3x3卷积核)
├── 中尺度ConvLSTM (5x5卷积核)  
└── 粗尺度ConvLSTM (7x7卷积核)
  ↓
尺度注意力: 学习不同尺度的权重
  ↓
多尺度特征融合: 加权拼接 → 卷积融合
  ↓
输出投影 + 残差连接
  ↓
输出: 预测SST
```

**特点:**
- 多尺度并行处理，捕捉不同空间尺度特征
- 尺度注意力机制自适应权重分配
- 更强的特征表达能力

## 文件结构

```
├── analyze_era5_features.py           # ERA5数据特征分析
├── preprocess_sst_pcs_era5.py         # 数据预处理脚本
├── eof_convlstm_model.py              # 更新的模型定义(包含新模型类)
├── train_sst_pcs_era5_convlstm.py     # 训练脚本
├── visualize_sst_pcs_era5_convlstm.py # 可视化脚本
├── compare_models.py                  # 更新的模型比较脚本
├── run_sst_pcs_era5_pipeline.py       # 完整流水线脚本
└── SST_PCs_ERA5_ConvLSTM_README.md    # 本文档
```

## 使用方法

### 前置条件

1. 确保已完成EOF分解：
```bash
python preprocess_and_eof.py
```

2. 确保数据文件存在：
- `SST-V2.nc`: 海表温度数据
- `data_ERA5.nc`: ERA5再分析数据

### 方法1: 使用完整流水线 (推荐)

```bash
# 运行完整流水线 - 训练两种模型
python run_sst_pcs_era5_pipeline.py --model-type both

# 只训练基础模型
python run_sst_pcs_era5_pipeline.py --model-type basic

# 只训练多尺度模型  
python run_sst_pcs_era5_pipeline.py --model-type multiscale

# 跳过训练，只进行可视化和比较
python run_sst_pcs_era5_pipeline.py --skip-training

# 跳过数据预处理
python run_sst_pcs_era5_pipeline.py --skip-preprocessing
```

### 方法2: 分步执行

```bash
# 1. ERA5特征分析
python analyze_era5_features.py

# 2. 数据预处理
python preprocess_sst_pcs_era5.py

# 3. 模型训练
python train_sst_pcs_era5_convlstm.py

# 4. 结果可视化
python visualize_sst_pcs_era5_convlstm.py

# 5. 模型比较
python compare_models.py
```

## 超参数配置

### 模型超参数

```python
# 基础模型
SSTWithPCsAndERA5ConvLSTMModel(
    num_pcs=10,           # 主成分数量
    seq_len=14,           # 输入序列长度
    pred_len=1,           # 预测长度
    hidden_dim=64,        # ConvLSTM隐藏维度
    kernel_size=(3, 3),   # 卷积核大小
    num_layers=2,         # ConvLSTM层数
    dropout=0.1           # Dropout率
)

# 多尺度模型
MultiScaleSSTWithPCsAndERA5ConvLSTMModel(
    # 参数同上，但内部使用多个不同尺度的卷积核
    # 细尺度: 3x3, 中尺度: 5x5, 粗尺度: 7x7
)
```

### 训练超参数

```python
seq_len = 14              # 输入序列长度
pred_len = 1              # 预测长度
batch_size = 8            # 批次大小(适应更大输入)
hidden_dim = 64           # 隐藏维度
learning_rate = 0.001     # 学习率
weight_decay = 1e-4       # 权重衰减
num_epochs = 80           # 训练轮数
patience = 15             # 早停耐心值
clip_grad = 1.0           # 梯度裁剪
```

## 输出结果

### 数据预处理结果
- `sst_pcs_era5_data/`: 预处理后的标准化数据
- `era5_analysis/`: ERA5特征分析结果

### 训练结果
- `sst_pcs_era5_results/best_sst_pcs_era5_*.pth`: 最佳模型权重
- `sst_pcs_era5_results/test_predictions_*.npy`: SST预测结果
- `sst_pcs_era5_figures/training_curves_*.png`: 训练曲线

### 可视化结果
- `sst_pcs_era5_visualization/sst_comparison_*.png`: SST对比图
- `sst_pcs_era5_visualization/rmse_map_*.png`: RMSE空间分布
- `sst_pcs_era5_visualization/sst_prediction_results_*.nc`: NetCDF结果

### 比较结果
- `model_comparison/performance_comparison.csv`: 性能比较表
- `model_comparison/evaluation_report.md`: 综合评估报告

## 技术优势

### 1. 多源信息融合
- **海洋信息**: SST场提供海洋状态
- **降维信息**: PCs提供主要模态特征  
- **大气信息**: ERA5提供大气强迫

### 2. 物理机制考虑
- 大气-海洋相互作用
- 风应力对SST的影响
- 气温与SST的热交换

### 3. 端到端优化
- 避免PCs预测误差累积
- 直接优化SST预测目标
- 更高的预测精度

### 4. 多尺度建模
- 捕捉不同空间尺度特征
- 自适应尺度权重学习
- 更强的泛化能力

## 性能评估

模型性能通过以下指标评估：

1. **SST预测精度**
   - RMSE: 均方根误差 (°C)
   - MAE: 平均绝对误差 (°C)

2. **空间分布误差**
   - RMSE空间分布图
   - 时间平均误差场

3. **时间序列性能**
   - 空间平均SST时间序列对比
   - 误差随时间变化

## 应用建议

### 1. 模型选择
- **基础模型**: 适用于计算资源有限的情况
- **多尺度模型**: 适用于需要更高精度的应用

### 2. 数据质量要求
- 确保SST、PCs和ERA5数据的时间同步
- 注意空间分辨率匹配
- 合理的数据标准化

### 3. 计算资源考虑
- GPU内存需求较大(输入通道数增加)
- 可适当减小批次大小
- 多尺度模型计算量更大

## 扩展方向

1. **更多大气变量**: 加入降水、湿度等变量
2. **时间尺度扩展**: 支持多步长期预测
3. **区域自适应**: 针对不同海域优化模型
4. **集成学习**: 结合多个模型的预测结果
5. **物理约束**: 加入物理定律约束

## 与其他方法的比较

| 方法 | 输入特征 | 预测目标 | 优势 | 劣势 |
|------|----------|----------|------|------|
| Transformer | PCs | PCs→SST重构 | 长期依赖建模 | 误差累积 |
| EOF-ConvLSTM | PCs | PCs→SST重构 | 时空建模 | 间接预测 |
| SST+PCs ConvLSTM | SST+PCs | 直接SST预测 | 端到端优化 | 缺少大气信息 |
| **SST+PCs+ERA5 ConvLSTM** | **SST+PCs+ERA5** | **直接SST预测** | **多源融合，物理机制** | **计算复杂度高** |

## 注意事项

1. **数据同步**: 确保所有数据源的时间对齐
2. **内存管理**: 注意GPU内存限制，可能需要减小批次大小
3. **特征重要性**: 通过注意力权重分析不同特征的贡献
4. **模型复杂度**: 平衡模型性能和计算效率

---

*本方案为海表温度预测提供了一种创新的多源数据融合深度学习方法，充分利用了大气-海洋相互作用的物理机制，为海洋预报和气候研究提供了新的技术路径。*
