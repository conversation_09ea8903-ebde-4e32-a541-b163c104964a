#!/usr/bin/env python3
"""
EOF加权空间映射流水线测试脚本
验证策略2的完整实现流程

作者: AI Assistant
日期: 2025-07-07
"""

import torch
import numpy as np
import os
import sys

# 添加父目录到路径
sys.path.append('..')

print("🧪 测试EOF加权空间映射流水线...")

def test_data_preprocessing():
    """测试数据预处理"""
    print("\n📊 测试数据预处理...")
    
    # 检查必要的输入文件
    required_files = [
        '../results/EOFs_k.npy',
        '../results/PCs_train.npy',
        '../SST-V2.nc',
        '../data_ERA5.nc'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        print("请先运行 ../preprocess_and_eof.py 生成EOF分解结果")
        return False
    
    print("✅ 所有必要文件存在")
    return True

def test_eof_weighted_features():
    """测试EOF加权特征生成"""
    print("\n🎯 测试EOF加权特征生成...")
    
    try:
        # 加载EOF分解结果
        EOFs_k = np.load('../results/EOFs_k.npy')
        PCs_train = np.load('../results/PCs_train.npy')
        
        print(f"EOF模态形状: {EOFs_k.shape}")
        print(f"训练集PCs形状: {PCs_train.shape}")
        
        # 模拟重塑EOF模态为空间形式
        n_vars = 2  # SST + T2m
        n_lat, n_lon = 100, 140  # 假设的空间维度
        k = EOFs_k.shape[1]
        
        # 检查维度一致性
        expected_spatial_points = n_vars * n_lat * n_lon
        actual_spatial_points = EOFs_k.shape[0]
        
        print(f"期望空间点数: {expected_spatial_points}")
        print(f"实际空间点数: {actual_spatial_points}")
        
        if expected_spatial_points == actual_spatial_points:
            print("✅ EOF模态维度正确，可以重塑为空间形式")
            
            # 模拟重塑过程
            EOFs_spatial = EOFs_k.reshape(n_vars, n_lat, n_lon, k)
            EOFs_sst = EOFs_spatial[0]  # SST的EOF模态
            
            print(f"SST EOF空间模态形状: {EOFs_sst.shape}")
            
            # 模拟创建加权特征
            time_steps = min(10, PCs_train.shape[1])  # 测试前10个时间步
            weighted_features = np.zeros((time_steps, k, n_lat, n_lon))
            
            for t in range(time_steps):
                for mode_idx in range(k):
                    pc_value = PCs_train[mode_idx, t]
                    weighted_features[t, mode_idx] = pc_value * EOFs_sst[:, :, mode_idx]
            
            print(f"加权特征形状: {weighted_features.shape}")
            print("✅ EOF加权特征生成逻辑正确")
            
        else:
            print("❌ EOF模态维度不匹配，需要检查数据预处理")
            return False
            
    except Exception as e:
        print(f"❌ EOF加权特征测试失败: {e}")
        return False
    
    return True

def test_model_architecture():
    """测试模型架构"""
    print("\n🧠 测试模型架构...")
    
    try:
        from eof_weighted_convlstm_model import EOFWeightedConvLSTMModel
        
        # 模型参数
        batch_size = 2
        seq_len = 14
        pred_len = 1
        lat, lon = 100, 140
        num_eof_modes = 10
        
        # 创建模拟输入数据
        sst_input = torch.randn(batch_size, seq_len, 1, lat, lon)
        era5_input = torch.randn(batch_size, seq_len, 4, lat, lon)
        eof_weighted_input = torch.randn(batch_size, seq_len, num_eof_modes, lat, lon)
        
        print(f"输入数据形状:")
        print(f"  SST: {sst_input.shape}")
        print(f"  ERA5: {era5_input.shape}")
        print(f"  EOF加权: {eof_weighted_input.shape}")
        
        # 创建模型
        model = EOFWeightedConvLSTMModel(
            num_eof_modes=num_eof_modes,
            seq_len=seq_len,
            pred_len=pred_len,
            hidden_dim=32  # 减小用于测试
        )
        
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 前向传播测试
        model.eval()
        with torch.no_grad():
            output = model(sst_input, era5_input, eof_weighted_input)
            print(f"输出形状: {output.shape}")
            print(f"输出范围: {output.min().item():.4f} - {output.max().item():.4f}")
        
        # 检查输出形状
        expected_shape = (batch_size, pred_len, lat, lon)
        if output.shape == expected_shape:
            print("✅ 模型输出形状正确")
        else:
            print(f"❌ 输出形状错误，期望: {expected_shape}, 实际: {output.shape}")
            return False
            
    except Exception as e:
        print(f"❌ 模型架构测试失败: {e}")
        return False
    
    return True

def test_data_loader():
    """测试数据加载器"""
    print("\n📦 测试数据加载器...")
    
    try:
        # 模拟数据集
        n_time = 100
        n_lat, n_lon = 100, 140
        seq_len = 14
        pred_len = 1
        
        # 创建模拟数据
        sst_data = np.random.randn(n_time, n_lat, n_lon)
        era5_data = np.random.randn(n_time, 4, n_lat, n_lon)
        eof_weighted_data = np.random.randn(n_time, 10, n_lat, n_lon)
        
        print(f"模拟数据形状:")
        print(f"  SST: {sst_data.shape}")
        print(f"  ERA5: {era5_data.shape}")
        print(f"  EOF加权: {eof_weighted_data.shape}")
        
        # 模拟数据集类
        class MockDataset:
            def __init__(self, sst_data, era5_data, eof_weighted_data, seq_len, pred_len):
                self.sst_data = sst_data
                self.era5_data = era5_data
                self.eof_weighted_data = eof_weighted_data
                self.seq_len = seq_len
                self.pred_len = pred_len
                self.n_samples = len(sst_data) - seq_len - pred_len + 1
            
            def __len__(self):
                return self.n_samples
            
            def __getitem__(self, idx):
                sst_input = self.sst_data[idx:idx+self.seq_len]
                sst_target = self.sst_data[idx+self.seq_len:idx+self.seq_len+self.pred_len]
                era5_input = self.era5_data[idx:idx+self.seq_len]
                eof_weighted_input = self.eof_weighted_data[idx:idx+self.seq_len]
                
                sst_input = sst_input[:, np.newaxis, :, :]  # 添加通道维度
                
                return (torch.FloatTensor(sst_input), 
                        torch.FloatTensor(era5_input), 
                        torch.FloatTensor(eof_weighted_input),
                        torch.FloatTensor(sst_target))
        
        # 创建数据集
        dataset = MockDataset(sst_data, era5_data, eof_weighted_data, seq_len, pred_len)
        print(f"数据集样本数: {len(dataset)}")
        
        # 测试数据加载
        sample = dataset[0]
        print(f"样本数据形状:")
        print(f"  SST输入: {sample[0].shape}")
        print(f"  ERA5输入: {sample[1].shape}")
        print(f"  EOF加权输入: {sample[2].shape}")
        print(f"  SST目标: {sample[3].shape}")
        
        print("✅ 数据加载器逻辑正确")
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        return False
    
    return True

def test_training_logic():
    """测试训练逻辑"""
    print("\n🎯 测试训练逻辑...")
    
    try:
        from eof_weighted_convlstm_model import EOFWeightedConvLSTMModel
        
        # 创建小型模型和数据用于测试
        model = EOFWeightedConvLSTMModel(
            num_eof_modes=5,
            seq_len=7,
            pred_len=1,
            hidden_dim=16
        )
        
        # 创建模拟数据
        batch_size = 2
        sst_input = torch.randn(batch_size, 7, 1, 50, 70)
        era5_input = torch.randn(batch_size, 7, 4, 50, 70)
        eof_input = torch.randn(batch_size, 7, 5, 50, 70)
        sst_target = torch.randn(batch_size, 1, 50, 70)
        
        # 测试前向传播
        model.train()
        output = model(sst_input, era5_input, eof_input)
        
        # 测试损失计算
        criterion = torch.nn.MSELoss()
        loss = criterion(output, sst_target)
        
        print(f"损失值: {loss.item():.6f}")
        
        # 测试反向传播
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print("✅ 训练逻辑测试通过")
        
    except Exception as e:
        print(f"❌ 训练逻辑测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始EOF加权空间映射流水线完整测试...")
    
    tests = [
        ("数据预处理", test_data_preprocessing),
        ("EOF加权特征", test_eof_weighted_features),
        ("模型架构", test_model_architecture),
        ("数据加载器", test_data_loader),
        ("训练逻辑", test_training_logic)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        if test_func():
            passed_tests += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*60}")
    print(f"测试总结: {passed_tests}/{total_tests} 个测试通过")
    print('='*60)
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！EOF加权空间映射流水线准备就绪")
        print("\n📋 下一步操作:")
        print("1. 运行 preprocess_eof_weighted_data.py 进行数据预处理")
        print("2. 运行 train_eof_weighted_convlstm.py 训练模型")
        print("3. 运行 visualize_eof_weighted_results.py 可视化结果")
    else:
        print("⚠️  部分测试失败，请检查相关组件")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 EOF加权空间映射策略2实现要点:")
        print("1. **物理意义**: 保持EOF模态的空间结构信息")
        print("2. **特征丰富**: 结合SST、ERA5和EOF加权特征")
        print("3. **自适应性**: 通过注意力机制动态融合特征")
        print("4. **计算效率**: 相比重构方法更加高效")
        print("5. **可解释性**: EOF加权场具有明确的物理含义")
        
        print("\n💡 预期改进效果:")
        print("- 相比纯SST模型: 显著提升预测精度")
        print("- 相比SST+ERA5模型: 增加物理约束和模态信息")
        print("- 相比完整PCs模型: 保持空间结构，减少噪声")
        
    print(f"\n📁 实验文件夹: eof_weighted_mapping_experiment/")
    print("包含完整的策略2实现代码和测试脚本")
