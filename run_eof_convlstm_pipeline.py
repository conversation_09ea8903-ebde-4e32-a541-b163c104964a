#!/usr/bin/env python3
"""
SST+PCs ConvLSTM完整流水线脚本
将EOF分解后的PCs作为额外特征与SST一起作为ConvLSTM模型的输入直接预测SST

作者: AI Assistant
日期: 2025-06-28
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"开始执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {description} 完成！用时: {duration:.2f}秒")
        
        # 打印输出的最后几行
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            print("输出摘要:")
            for line in lines[-5:]:  # 显示最后5行
                print(f"  {line}")
                
        return True
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ {description} 失败！用时: {duration:.2f}秒")
        print(f"错误代码: {e.returncode}")
        print(f"错误信息: {e.stderr}")
        
        return False

def check_prerequisites():
    """检查前置条件"""
    print("检查前置条件...")
    
    # 检查必要的数据文件
    required_files = [
        'SST-V2.nc',
        'data_ERA5.nc',
        'results/PCs_train.npy',
        'results/PCs_val.npy', 
        'results/PCs_test.npy',
        'results/EOFs_k.npy',
        'results/X_mean.npy',
        'results/sst_mean.npy',
        'results/sst_std.npy'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        print("\n请先运行数据预处理和EOF分解:")
        print("  python preprocess_and_eof.py")
        return False
    
    print("✅ 所有前置条件满足")
    return True

def main():
    parser = argparse.ArgumentParser(description='SST+PCs ConvLSTM完整流水线')
    parser.add_argument('--model-type', choices=['basic', 'multiscale', 'both'],
                       default='both', help='选择模型类型')
    parser.add_argument('--skip-training', action='store_true',
                       help='跳过训练，直接进行可视化和比较')
    parser.add_argument('--skip-visualization', action='store_true',
                       help='跳过可视化，只进行训练')
    parser.add_argument('--skip-comparison', action='store_true',
                       help='跳过模型比较')

    args = parser.parse_args()

    print("SST+PCs ConvLSTM完整流水线")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"模型类型: {args.model_type}")

    # 检查前置条件
    if not check_prerequisites():
        sys.exit(1)

    # 创建必要的目录
    os.makedirs('sst_pcs_convlstm_results', exist_ok=True)
    os.makedirs('sst_pcs_convlstm_figures', exist_ok=True)
    os.makedirs('sst_pcs_convlstm_visualization', exist_ok=True)
    os.makedirs('model_comparison', exist_ok=True)
    
    success_count = 0
    total_steps = 0
    
    # 步骤1: 训练SST+PCs ConvLSTM模型
    if not args.skip_training:
        model_types = ['basic', 'multiscale'] if args.model_type == 'both' else [args.model_type]

        for model_type in model_types:
            total_steps += 1
            print(f"\n训练 {model_type} 模型...")

            # 修改训练脚本中的模型类型
            with open('train_eof_convlstm.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # 替换模型类型
            content = content.replace('model_type = "basic"', f'model_type = "{model_type}"')

            with open(f'train_sst_pcs_convlstm_{model_type}.py', 'w', encoding='utf-8') as f:
                f.write(content)

            if run_command(f'python train_sst_pcs_convlstm_{model_type}.py',
                          f'训练SST+PCs ConvLSTM {model_type}模型'):
                success_count += 1

            # 清理临时文件
            if os.path.exists(f'train_sst_pcs_convlstm_{model_type}.py'):
                os.remove(f'train_sst_pcs_convlstm_{model_type}.py')

    # 步骤2: SST预测结果可视化
    if not args.skip_visualization:
        model_types = ['basic', 'multiscale'] if args.model_type == 'both' else [args.model_type]

        for model_type in model_types:
            total_steps += 1
            print(f"\n可视化 {model_type} 模型的SST预测结果...")

            # 修改可视化脚本中的模型类型
            with open('visualize_sst_pcs_convlstm.py', 'r', encoding='utf-8') as f:
                content = f.read()

            # 替换模型类型
            content = content.replace('model_type = "basic"', f'model_type = "{model_type}"')

            with open(f'visualize_sst_pcs_convlstm_{model_type}.py', 'w', encoding='utf-8') as f:
                f.write(content)

            if run_command(f'python visualize_sst_pcs_convlstm_{model_type}.py',
                          f'可视化SST+PCs ConvLSTM {model_type}模型的预测结果'):
                success_count += 1

            # 清理临时文件
            if os.path.exists(f'visualize_sst_pcs_convlstm_{model_type}.py'):
                os.remove(f'visualize_sst_pcs_convlstm_{model_type}.py')
    
    # 步骤3: 模型比较
    if not args.skip_comparison:
        total_steps += 1
        if run_command('python compare_models.py', '模型性能比较'):
            success_count += 1
    
    # 总结
    print(f"\n{'='*60}")
    print("流水线执行完成！")
    print(f"成功步骤: {success_count}/{total_steps}")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count == total_steps:
        print("✅ 所有步骤都成功完成！")
        
        print("\n生成的结果文件:")
        print("📁 sst_pcs_convlstm_results/")
        print("  - 训练好的模型权重")
        print("  - SST直接预测结果")
        print("  - 标准化参数")

        print("📁 sst_pcs_convlstm_figures/")
        print("  - 训练损失曲线")
        print("  - SST预测对比图")
        print("  - 空间误差分布图")

        print("📁 sst_pcs_convlstm_visualization/")
        print("  - SST预测可视化结果")
        print("  - 误差统计")
        print("  - NetCDF格式的预测结果")

        print("📁 model_comparison/")
        print("  - 模型性能比较表")
        print("  - 性能比较图表")
        print("  - 综合评估报告")

        print("\n🎉 SST+PCs ConvLSTM流水线执行成功！")
        
    else:
        print("⚠️  部分步骤执行失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
