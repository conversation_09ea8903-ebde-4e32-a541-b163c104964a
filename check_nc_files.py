import xarray as xr
import numpy as np
import matplotlib.pyplot as plt

# 读取SST数据
print("读取SST数据...")
sst_data = xr.open_dataset('SST-V2.nc')
print("\nSST数据结构:")
print(sst_data)
print("\nSST数据维度:")
for dim_name, dim_size in sst_data.dims.items():
    print(f"{dim_name}: {dim_size}")

# 读取ERA5数据
print("\n读取ERA5数据...")
era5_data = xr.open_dataset('data_ERA5.nc')
print("\nERA5数据结构:")
print(era5_data)
print("\nERA5数据维度:")
for dim_name, dim_size in era5_data.dims.items():
    print(f"{dim_name}: {dim_size}")

# 关闭数据集
sst_data.close()
era5_data.close() 

