import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import os
import time
from sklearn.metrics import mean_squared_error, mean_absolute_error
import pandas as pd
from tqdm import tqdm

# 导入统一的可视化配置
from visualization_config import (
    SST_COLORMAP, SST_VMIN, SST_VMAX,
    ERROR_COLORMAP, ERROR_VMIN, ERROR_VMAX,
    RMSE_COLORMAP, RMSE_VMIN, RMSE_VMAX,
    TIMESERIES_COLORS, FIGURE_SIZES, FONT_SIZES, DPI,
    get_sample_date_mapping, format_date_for_title,
    get_adaptive_sst_range, get_adaptive_error_range
)


print("开始比较不同模型的性能...")

# 创建结果文件夹
os.makedirs('model_comparison', exist_ok=True)

# 模型列表和对应的结果文件
models = {
    'Transformer': {
        'predictions': 'model_results/test_predictions.npy',
        'targets': 'model_results/test_targets.npy',
        'sst_pred': 'reconstruction_results/sst_pred_celsius.npy',
        'sst_true': 'reconstruction_results/sst_true_celsius.npy',
        'sst_error': 'reconstruction_results/sst_error_celsius.npy',
        'type': 'pcs_prediction'  # 预测PCs然后重构SST
    },
    'EOF-ConvLSTM-Basic': {
        'predictions': 'eof_convlstm_results/test_predictions_basic.npy',
        'targets': 'eof_convlstm_results/test_targets_basic.npy',
        'sst_pred': 'eof_convlstm_reconstruction/sst_pred_celsius_basic.npy',
        'sst_true': 'eof_convlstm_reconstruction/sst_true_celsius_basic.npy',
        'sst_error': 'eof_convlstm_reconstruction/sst_error_celsius_basic.npy',
        'type': 'pcs_prediction'  # 预测PCs然后重构SST
    },
    'EOF-ConvLSTM-Spatial': {
        'predictions': 'eof_convlstm_results/test_predictions_spatial.npy',
        'targets': 'eof_convlstm_results/test_targets_spatial.npy',
        'sst_pred': 'eof_convlstm_reconstruction/sst_pred_celsius_spatial.npy',
        'sst_true': 'eof_convlstm_reconstruction/sst_true_celsius_spatial.npy',
        'sst_error': 'eof_convlstm_reconstruction/sst_error_celsius_spatial.npy',
        'type': 'pcs_prediction'  # 预测PCs然后重构SST
    },
    'SST+PCs-ConvLSTM-Basic': {
        'predictions': None,  # 直接预测SST，没有PCs预测
        'targets': None,
        'sst_pred': 'sst_pcs_convlstm_results/test_predictions_basic.npy',
        'sst_true': 'sst_pcs_convlstm_results/test_targets_basic.npy',
        'sst_error': None,  # 需要计算
        'type': 'direct_sst'  # 直接预测SST
    },
    'SST+PCs-ConvLSTM-MultiScale': {
        'predictions': None,  # 直接预测SST，没有PCs预测
        'targets': None,
        'sst_pred': 'sst_pcs_convlstm_results/test_predictions_multiscale.npy',
        'sst_true': 'sst_pcs_convlstm_results/test_targets_multiscale.npy',
        'sst_error': None,  # 需要计算
        'type': 'direct_sst'  # 直接预测SST
    },
    'SST+PCs+ERA5-ConvLSTM-Basic': {
        'predictions': None,  # 直接预测SST，没有PCs预测
        'targets': None,
        'sst_pred': 'sst_pcs_era5_results/test_predictions_sst_pcs_era5_basic.npy',
        'sst_true': 'sst_pcs_era5_results/test_targets_sst_pcs_era5_basic.npy',
        'sst_error': None,  # 需要计算
        'type': 'direct_sst'  # 直接预测SST
    },
    'SST+PCs+ERA5-ConvLSTM-MultiScale': {
        'predictions': None,  # 直接预测SST，没有PCs预测
        'targets': None,
        'sst_pred': 'sst_pcs_era5_results/test_predictions_sst_pcs_era5_multiscale.npy',
        'sst_true': 'sst_pcs_era5_results/test_targets_sst_pcs_era5_multiscale.npy',
        'sst_error': None,  # 需要计算
        'type': 'direct_sst'  # 直接预测SST
    },
    'SST+ERA5-ConvLSTM-Basic (Ablation)': {
        'predictions': None,  # 消融实验：移除PCs
        'targets': None,
        'sst_pred': 'sst_era5_ablation_results/test_predictions_sst_era5_ablation_basic.npy',
        'sst_true': 'sst_era5_ablation_results/test_targets_sst_era5_ablation_basic.npy',
        'sst_error': None,  # 需要计算
        'type': 'direct_sst'  # 直接预测SST
    },
    'SST+ERA5-ConvLSTM-MultiScale (Ablation)': {
        'predictions': None,  # 消融实验：移除PCs
        'targets': None,
        'sst_pred': 'sst_era5_ablation_results/test_predictions_sst_era5_ablation_multiscale.npy',
        'sst_true': 'sst_era5_ablation_results/test_targets_sst_era5_ablation_multiscale.npy',
        'sst_error': None,  # 需要计算
        'type': 'direct_sst'  # 直接预测SST
    },
    'SST-Only-ConvLSTM-Basic (Baseline)': {
        'predictions': None,  # 基线模型：仅SST
        'targets': None,
        'sst_pred': 'sst_only_results/test_predictions_sst_only_basic.npy',
        'sst_true': 'sst_only_results/test_targets_sst_only_basic.npy',
        'sst_error': None,  # 需要计算
        'type': 'direct_sst'  # 直接预测SST
    }
}

# 存储所有模型的评估结果
results = {}

print("📊 加载和评估各模型结果...")

with tqdm(models.items(), desc='评估模型', ncols=120) as pbar:
    for model_name, paths in pbar:
        pbar.set_description(f'评估 {model_name}')

        try:
            model_type = paths['type']

            # 根据模型类型处理PCs预测结果
            if model_type == 'pcs_prediction':
                # 加载PCs预测结果
                if paths['predictions'] and os.path.exists(paths['predictions']) and paths['targets'] and os.path.exists(paths['targets']):
                    predictions = np.load(paths['predictions'])
                    targets = np.load(paths['targets'])

                    # 计算PCs预测误差
                    pc_mse = mean_squared_error(targets.reshape(-1), predictions.reshape(-1))
                    pc_mae = mean_absolute_error(targets.reshape(-1), predictions.reshape(-1))

                    tqdm.write(f"  ✅ {model_name} PCs预测 - MSE: {pc_mse:.6f}, MAE: {pc_mae:.6f}")
                else:
                    pc_mse, pc_mae = np.nan, np.nan
                    predictions, targets = None, None
                    tqdm.write(f"  ❌ {model_name} PCs预测结果文件不存在")
            else:
                # 直接SST预测模型没有PCs预测
                pc_mse, pc_mae = np.nan, np.nan
                predictions, targets = None, None
                tqdm.write(f"  ℹ️ {model_name} 直接预测SST，无PCs预测结果")

            # 加载SST预测/重构结果
            if os.path.exists(paths['sst_pred']) and os.path.exists(paths['sst_true']):
                sst_pred = np.load(paths['sst_pred'])
                sst_true = np.load(paths['sst_true'])

                # 计算或加载SST误差
                if paths['sst_error'] and os.path.exists(paths['sst_error']):
                    sst_error = np.load(paths['sst_error'])
                else:
                    # 计算SST误差
                    sst_error = sst_pred - sst_true

                # 计算SST误差统计
                sst_rmse = np.sqrt(np.mean(sst_error ** 2))
                sst_mae = np.mean(np.abs(sst_error))

                if model_type == 'pcs_prediction':
                    tqdm.write(f"  ✅ {model_name} SST重构 - RMSE: {sst_rmse:.4f}°C, MAE: {sst_mae:.4f}°C")
                else:
                    tqdm.write(f"  ✅ {model_name} SST直接预测 - RMSE: {sst_rmse:.4f}°C, MAE: {sst_mae:.4f}°C")
            else:
                sst_rmse, sst_mae = np.nan, np.nan
                sst_pred, sst_true, sst_error = None, None, None
                tqdm.write(f"  ❌ {model_name} SST结果文件不存在")

            # 存储结果
            results[model_name] = {
                'pc_mse': pc_mse,
                'pc_mae': pc_mae,
                'sst_rmse': sst_rmse,
                'sst_mae': sst_mae,
                'predictions': predictions,
                'targets': targets,
                'sst_pred': sst_pred,
                'sst_true': sst_true,
                'sst_error': sst_error,
                'model_type': model_type
            }

        except Exception as e:
            tqdm.write(f"  ❌ 加载 {model_name} 结果时出错: {e}")
            results[model_name] = {
                'pc_mse': np.nan,
                'pc_mae': np.nan,
                'sst_rmse': np.nan,
                'sst_mae': np.nan,
                'predictions': None,
                'targets': None,
                'sst_pred': None,
                'sst_true': None,
                'sst_error': None,
                'model_type': 'unknown'
            }

# 创建性能比较表
print("\n创建性能比较表...")
comparison_data = []
for model_name, result in results.items():
    comparison_data.append({
        '模型': model_name,
        'PCs MSE': result['pc_mse'],
        'PCs MAE': result['pc_mae'],
        'SST RMSE (°C)': result['sst_rmse'],
        'SST MAE (°C)': result['sst_mae']
    })

df_comparison = pd.DataFrame(comparison_data)
print("\n模型性能比较:")
print(df_comparison.to_string(index=False, float_format='%.6f'))

# 保存比较表
df_comparison.to_csv('model_comparison/performance_comparison.csv', index=False)

# 绘制性能比较图表
print("\n📊 生成性能比较图表...")

chart_tasks = [
    ('PCs预测误差比较', 'pcs_error_comparison'),
    ('SST误差比较', 'sst_error_comparison'),
    ('时间序列预测对比', 'prediction_comparison')
]

with tqdm(chart_tasks, desc='生成图表', ncols=100) as pbar:
    for task_name, filename_prefix in pbar:
        pbar.set_description(f'生成{task_name}')

        if task_name == 'PCs预测误差比较':
            # 1. PCs预测误差比较
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # MSE比较
            valid_models_mse = [(name, result['pc_mse']) for name, result in results.items() if not np.isnan(result['pc_mse'])]
            if valid_models_mse:
                model_names_mse, mse_values = zip(*valid_models_mse)
                bars1 = ax1.bar(model_names_mse, mse_values, color=['skyblue', 'lightcoral', 'lightgreen'][:len(model_names_mse)])
                ax1.set_title('PCs预测均方误差(MSE)比较', fontsize=FONT_SIZES['title'])
                ax1.set_ylabel('MSE', fontsize=FONT_SIZES['label'])
                ax1.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, value in zip(bars1, mse_values):
                    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(mse_values)*0.01,
                            f'{value:.6f}', ha='center', va='bottom', fontsize=FONT_SIZES['tick'])

            # MAE比较
            valid_models_mae = [(name, result['pc_mae']) for name, result in results.items() if not np.isnan(result['pc_mae'])]
            if valid_models_mae:
                model_names_mae, mae_values = zip(*valid_models_mae)
                bars2 = ax2.bar(model_names_mae, mae_values, color=['skyblue', 'lightcoral', 'lightgreen'][:len(model_names_mae)])
                ax2.set_title('PCs预测平均绝对误差(MAE)比较', fontsize=FONT_SIZES['title'])
                ax2.set_ylabel('MAE', fontsize=FONT_SIZES['label'])
                ax2.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, value in zip(bars2, mae_values):
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(mae_values)*0.01,
                            f'{value:.6f}', ha='center', va='bottom', fontsize=FONT_SIZES['tick'])

            plt.tight_layout()
            plt.savefig('model_comparison/pcs_error_comparison.png', dpi=DPI, bbox_inches='tight')
            plt.close()

        elif task_name == 'SST误差比较':
            # 2. SST重构误差比较
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # RMSE比较
            valid_models_rmse = [(name, result['sst_rmse']) for name, result in results.items() if not np.isnan(result['sst_rmse'])]
            if valid_models_rmse:
                model_names_rmse, rmse_values = zip(*valid_models_rmse)
                colors = plt.cm.Set3(np.linspace(0, 1, len(model_names_rmse)))
                bars1 = ax1.bar(model_names_rmse, rmse_values, color=colors)
                ax1.set_title('SST预测/重构均方根误差(RMSE)比较', fontsize=FONT_SIZES['title'])
                ax1.set_ylabel('RMSE (°C)', fontsize=FONT_SIZES['label'])
                ax1.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, value in zip(bars1, rmse_values):
                    ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(rmse_values)*0.01,
                            f'{value:.4f}', ha='center', va='bottom', fontsize=FONT_SIZES['tick'])

            # MAE比较
            valid_models_sst_mae = [(name, result['sst_mae']) for name, result in results.items() if not np.isnan(result['sst_mae'])]
            if valid_models_sst_mae:
                model_names_sst_mae, sst_mae_values = zip(*valid_models_sst_mae)
                colors = plt.cm.Set3(np.linspace(0, 1, len(model_names_sst_mae)))
                bars2 = ax2.bar(model_names_sst_mae, sst_mae_values, color=colors)
                ax2.set_title('SST预测/重构平均绝对误差(MAE)比较', fontsize=FONT_SIZES['title'])
                ax2.set_ylabel('MAE (°C)', fontsize=FONT_SIZES['label'])
                ax2.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, value in zip(bars2, sst_mae_values):
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(sst_mae_values)*0.01,
                            f'{value:.4f}', ha='center', va='bottom', fontsize=FONT_SIZES['tick'])

            plt.tight_layout()
            plt.savefig('model_comparison/sst_error_comparison.png', dpi=DPI, bbox_inches='tight')
            plt.close()

        elif task_name == '时间序列预测对比':
            # 3. 时间序列预测对比（选择前3个主成分）
            for pc_idx in range(min(3, 10)):  # 假设有10个主成分
                plt.figure(figsize=(15, 8))

                # 获取有效的模型数据
                valid_models = [(name, result) for name, result in results.items()
                               if result['predictions'] is not None and result['targets'] is not None]

                if valid_models:
                    # 使用第一个有效模型的targets作为真实值
                    true_values = valid_models[0][1]['targets'][:, 0, pc_idx]
                    time_steps = range(len(true_values))

                    # 绘制真实值
                    plt.plot(time_steps, true_values, label='真实值', color='black', linewidth=2)

                    # 绘制各模型的预测值
                    colors = ['blue', 'red', 'green', 'orange', 'purple']
                    for i, (model_name, result) in enumerate(valid_models):
                        if result['predictions'] is not None:
                            pred_values = result['predictions'][:, 0, pc_idx]
                            plt.plot(time_steps, pred_values, label=f'{model_name}预测',
                                    color=colors[i % len(colors)], alpha=0.7, linewidth=1.5)

                    plt.xlabel('时间步', fontsize=FONT_SIZES['label'])
                    plt.ylabel(f'PC{pc_idx+1}值', fontsize=FONT_SIZES['label'])
                    plt.title(f'主成分{pc_idx+1}预测结果对比', fontsize=FONT_SIZES['title'])
                    plt.legend(fontsize=FONT_SIZES['tick'])
                    plt.grid(True, alpha=0.3)
                    plt.tight_layout()
                    plt.savefig(f'model_comparison/pc{pc_idx+1}_prediction_comparison.png', dpi=DPI, bbox_inches='tight')
                    plt.close()

# 生成综合评估报告
print("\n📝 生成综合评估报告...")
with tqdm(total=1, desc='生成报告', ncols=100) as pbar:
    report = f"""
# 模型性能比较报告

## 评估指标说明
- **PCs MSE/MAE**: 主成分时间系数预测的均方误差和平均绝对误差
- **SST RMSE/MAE**: 海表温度场预测/重构的均方根误差和平均绝对误差（摄氏度）

## 性能比较结果

{df_comparison.to_string(index=False, float_format='%.6f')}

## 模型分析

### Transformer模型
- 基于注意力机制的序列到序列预测
- 直接在主成分空间进行预测
- 优势：能够捕捉长期依赖关系
- 方法：PCs预测 → EOF重构 → SST场

### EOF-ConvLSTM-Basic模型
- 将主成分映射到空间表示，使用ConvLSTM进行时空建模
- 优势：结合了空间卷积和时间记忆能力
- 特点：使用全连接层进行空间映射
- 方法：PCs预测 → EOF重构 → SST场

### EOF-ConvLSTM-Spatial模型
- 直接使用EOF空间模态进行空间表示
- 优势：保持了EOF的物理意义
- 特点：更直接的空间-时间建模方式
- 方法：PCs预测 → EOF重构 → SST场

### SST+PCs-ConvLSTM-Basic模型
- 将PCs作为额外特征与SST一起输入ConvLSTM
- 优势：端到端训练，避免误差累积
- 特点：直接预测SST，无需重构步骤
- 方法：SST+PCs → ConvLSTM → 直接SST预测

### SST+PCs-ConvLSTM-MultiScale模型
- 多尺度ConvLSTM架构，同时处理细粒度和粗粒度特征
- 优势：更强的特征表达能力
- 特点：并行多尺度处理，特征融合
- 方法：SST+PCs → 多尺度ConvLSTM → 直接SST预测

## 建议
1. **精度优先**: 选择RMSE最低的模型
2. **效率优先**: 选择直接SST预测模型（避免重构步骤）
3. **稳定性**: 考虑多个指标的综合表现
4. **集成方法**: 可以尝试集成多个模型的预测结果

## 生成时间
{time.strftime('%Y-%m-%d %H:%M:%S')}
"""

    with open('model_comparison/evaluation_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    pbar.update(1)

print(f"\n{'='*80}")
print("🎉 模型比较分析完成！")
print(f"📁 结果保存在 model_comparison/ 目录中:")
print("  📊 performance_comparison.csv: 性能比较表")
print("  📈 pcs_error_comparison.png: PCs预测误差比较图")
print("  📈 sst_error_comparison.png: SST误差比较图")
print("  📈 pc*_prediction_comparison.png: 主成分预测对比图")
print("  📝 evaluation_report.md: 综合评估报告")
print(f"{'='*80}")
