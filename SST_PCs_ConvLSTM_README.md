# SST+PCs ConvLSTM 直接SST预测方案

## 概述

本方案实现了将EOF分解后的主成分(PCs)作为额外特征与SST一起作为ConvLSTM模型输入，直接预测SST的创新方法。该方案充分利用了EOF降维的信息来增强SST预测性能。

## 核心思想

### 传统方法 vs 新方案

**传统Transformer方法:**
```
PCs时间序列 → Transformer → 预测PCs → EOF重构 → SST场
```

**EOF-ConvLSTM方法:**
```
PCs时间序列 → 空间化表示 → ConvLSTM → 预测PCs → EOF重构 → SST场
```

**SST+PCs ConvLSTM方法 (本方案):**
```
SST场 + PCs特征 → ConvLSTM → 直接预测SST场
```

### 技术创新点

1. **多模态输入**: 同时使用SST空间场和PCs时间特征
2. **直接预测**: 绕过PCs预测和重构步骤，直接预测SST
3. **特征融合**: 在卷积层面融合空间和降维特征
4. **端到端训练**: 从输入到输出的完整优化

## 模型架构

### 1. 基础SST+PCs ConvLSTM模型 (SSTWithPCsConvLSTMModel)

```python
输入: [SST场, PCs特征] [batch_size, seq_len, 1+num_pcs, lat, lon]
  ↓
ConvLSTM: 时空序列建模 (输入通道: 1+num_pcs)
  ↓
输出投影: Conv2d层序列 → 单通道SST
  ↓
残差连接: 与输入最后时间步SST相加
  ↓
输出: 预测SST [batch_size, pred_len, lat, lon]
```

**特点:**
- 直接在原始空间分辨率上建模
- 保持SST的空间结构信息
- PCs作为额外通道提供全局信息

### 2. 多尺度SST+PCs ConvLSTM模型 (MultiScaleSSTWithPCsConvLSTMModel)

```python
输入: [SST场, PCs特征] [batch_size, seq_len, 1+num_pcs, lat, lon]
  ↓
并行ConvLSTM分支:
├── 细尺度ConvLSTM (3x3卷积核)
└── 粗尺度ConvLSTM (5x5卷积核)
  ↓
特征融合: 连接多尺度特征
  ↓
输出投影: 融合特征 → 单通道SST
  ↓
残差连接: 与输入最后时间步SST相加
  ↓
输出: 预测SST [batch_size, pred_len, lat, lon]
```

**特点:**
- 多尺度时空建模
- 捕捉不同空间尺度的特征
- 更强的表达能力

## 数据处理流程

### 1. 数据准备
```python
# SST数据标准化
sst_norm = (sst - sst_mean) / sst_std

# PCs数据标准化
pcs_scaled = StandardScaler().fit_transform(pcs)

# 数据组合
combined_input = [sst_norm, pcs_spatial_broadcast]
```

### 2. 空间特征融合
```python
# PCs广播到空间维度
pcs_spatial = broadcast_to(pcs, (seq_len, num_pcs, lat, lon))

# 与SST合并
input_tensor = concatenate([sst, pcs_spatial], axis=channel_dim)
```

## 文件结构

```
├── eof_convlstm_model.py              # 更新的ConvLSTM模型定义
├── train_eof_convlstm.py              # 修改的训练脚本
├── visualize_sst_pcs_convlstm.py      # SST预测可视化脚本
├── compare_models.py                  # 更新的模型比较脚本
├── run_eof_convlstm_pipeline.py       # 更新的完整流水线脚本
└── SST_PCs_ConvLSTM_README.md         # 本文档
```

## 使用方法

### 前置条件

确保已完成EOF分解：
```bash
python preprocess_and_eof.py
```

### 方法1: 使用完整流水线

```bash
# 训练两种模型并进行比较
python run_eof_convlstm_pipeline.py --model-type both

# 只训练基础模型
python run_eof_convlstm_pipeline.py --model-type basic

# 只训练多尺度模型
python run_eof_convlstm_pipeline.py --model-type multiscale

# 跳过训练，只进行可视化和比较
python run_eof_convlstm_pipeline.py --skip-training
```

### 方法2: 分步执行

```bash
# 1. 训练模型
python train_eof_convlstm.py

# 2. 可视化结果
python visualize_sst_pcs_convlstm.py

# 3. 模型比较
python compare_models.py
```

## 超参数配置

### 模型超参数

```python
# 基础模型
SSTWithPCsConvLSTMModel(
    num_pcs=10,           # 主成分数量
    seq_len=14,           # 输入序列长度
    pred_len=1,           # 预测长度
    hidden_dim=64,        # ConvLSTM隐藏维度
    kernel_size=(3, 3),   # 卷积核大小
    num_layers=2,         # ConvLSTM层数
    dropout=0.1           # Dropout率
)

# 多尺度模型
MultiScaleSSTWithPCsConvLSTMModel(
    num_pcs=10,           # 主成分数量
    seq_len=14,           # 输入序列长度
    pred_len=1,           # 预测长度
    hidden_dim=64,        # ConvLSTM隐藏维度
    kernel_size=(3, 3),   # 基础卷积核大小
    num_layers=2,         # ConvLSTM层数
    dropout=0.1           # Dropout率
)
```

### 训练超参数

```python
batch_size = 16           # 减小批次大小（数据更大）
learning_rate = 0.001
weight_decay = 1e-4
num_epochs = 80
patience = 15
clip_grad = 1.0
```

## 输出结果

### 训练结果
- `sst_pcs_convlstm_results/best_sst_pcs_convlstm_{model_type}.pth`: 最佳模型权重
- `sst_pcs_convlstm_results/test_predictions_{model_type}.npy`: SST预测结果(°C)
- `sst_pcs_convlstm_figures/training_curves_{model_type}.png`: 训练曲线

### 可视化结果
- `sst_pcs_convlstm_visualization/sst_comparison_*.png`: SST对比图
- `sst_pcs_convlstm_visualization/sst_prediction_results_{model_type}.nc`: NetCDF格式结果
- `sst_pcs_convlstm_visualization/rmse_map_{model_type}.png`: RMSE分布图

### 比较结果
- `model_comparison/performance_comparison.csv`: 性能比较表
- `model_comparison/sst_error_comparison.png`: SST误差比较
- `model_comparison/evaluation_report.md`: 综合评估报告

## 技术优势

### 1. 直接预测优势
- 避免PCs预测误差累积
- 端到端优化，减少信息损失
- 更直接的目标导向训练

### 2. 多模态融合
- SST提供空间结构信息
- PCs提供全局模态信息
- 在卷积层面进行特征融合

### 3. 空间保真度
- 在原始空间分辨率上预测
- 保持SST场的空间细节
- 避免重构过程的平滑效应

### 4. 计算效率
- 单步预测，无需迭代重构
- 并行处理空间信息
- 更快的推理速度

## 性能评估

模型性能通过以下指标评估：

1. **SST预测精度**
   - RMSE: 均方根误差 (°C)
   - MAE: 平均绝对误差 (°C)

2. **空间分布误差**
   - RMSE空间分布图
   - 时间平均误差场

3. **时间序列性能**
   - 空间平均SST时间序列对比
   - 误差随时间变化

## 应用建议

### 1. 模型选择
- **基础模型**: 适用于计算资源有限的情况
- **多尺度模型**: 适用于需要更高精度的应用

### 2. 数据预处理
- 确保SST和PCs的时间对齐
- 合理的数据标准化
- 注意空间分辨率匹配

### 3. 超参数调优
- `hidden_dim`: 影响模型容量
- `num_layers`: 影响时序建模深度
- `batch_size`: 根据GPU内存调整

## 扩展方向

1. **注意力机制**: 在PCs特征上加入注意力权重
2. **多变量预测**: 同时预测SST和其他海洋变量
3. **长期预测**: 扩展到多步预测
4. **自适应融合**: 学习SST和PCs的最优融合权重

## 与其他方法的比较

| 方法 | 预测目标 | 优势 | 劣势 |
|------|----------|------|------|
| Transformer | PCs → SST重构 | 长期依赖建模 | 误差累积 |
| EOF-ConvLSTM | PCs → SST重构 | 时空建模 | 间接预测 |
| **SST+PCs ConvLSTM** | **直接SST预测** | **端到端，无误差累积** | **内存需求大** |

## 注意事项

1. 确保SST和PCs数据的时间同步
2. 注意GPU内存限制，可能需要减小批次大小
3. 合理设置学习率和正则化参数
4. 监控训练过程中的梯度变化

---

*本方案为SST预测提供了一种新的端到端深度学习方法，充分利用了EOF降维信息和ConvLSTM的时空建模能力。*
