#!/usr/bin/env python3
"""
改进的SST+PCs+ERA5 ConvLSTM模型训练脚本
基于消融实验结果的快速改进方案

作者: AI Assistant  
日期: 2025-07-02
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False
import os
from tqdm import tqdm
from sklearn.metrics import mean_squared_error, mean_absolute_error

# 导入改进模型
from improve_sst_pcs_era5_model import ImprovedSSTWithPCsAndERA5ConvLSTMModel

print("开始训练改进的SST+PCs+ERA5 ConvLSTM模型...")

# 创建结果文件夹
os.makedirs('improved_sst_pcs_era5_results', exist_ok=True)
os.makedirs('improved_sst_pcs_era5_figures', exist_ok=True)

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载预处理后的数据
print("加载预处理后的数据...")

# SST数据
sst_train = np.load('sst_pcs_era5_data/sst_train_norm.npy')
sst_val = np.load('sst_pcs_era5_data/sst_val_norm.npy')
sst_test = np.load('sst_pcs_era5_data/sst_test_norm.npy')

# ERA5数据
era5_vars = ['u10', 'v10', 't2m', 'msl']
era5_train = {}
era5_val = {}
era5_test = {}

for var in era5_vars:
    era5_train[var] = np.load(f'sst_pcs_era5_data/{var}_train_norm.npy')
    era5_val[var] = np.load(f'sst_pcs_era5_data/{var}_val_norm.npy')
    era5_test[var] = np.load(f'sst_pcs_era5_data/{var}_test_norm.npy')

# PCs数据
pcs_train = np.load('sst_pcs_era5_data/pcs_train_scaled.npy')
pcs_val = np.load('sst_pcs_era5_data/pcs_val_scaled.npy')
pcs_test = np.load('sst_pcs_era5_data/pcs_test_scaled.npy')

print(f"数据加载完成")

# 数据集类 (重用原有的)
from train_sst_pcs_era5_convlstm import SSTWithPCsAndERA5Dataset

# 改进的超参数设置
seq_len = 14
pred_len = 1
batch_size = 8
hidden_dim = 64
num_layers = 2
learning_rate = 0.0008  # 略微降低学习率
weight_decay = 2e-4     # 增加正则化
num_epochs = 60         # 减少epoch数
patience = 10           # 减少patience
clip_grad = 0.8         # 更严格的梯度裁剪
dropout = 0.15          # 增加dropout

print(f"改进的超参数设置:")
print(f"  学习率: {learning_rate} (降低)")
print(f"  权重衰减: {weight_decay} (增加)")
print(f"  Dropout: {dropout} (增加)")
print(f"  梯度裁剪: {clip_grad} (更严格)")
print(f"  早停patience: {patience} (减少)")

# 创建数据集和数据加载器
train_dataset = SSTWithPCsAndERA5Dataset(sst_train, pcs_train, era5_train, seq_len, pred_len)
val_dataset = SSTWithPCsAndERA5Dataset(sst_val, pcs_val, era5_val, seq_len, pred_len)
test_dataset = SSTWithPCsAndERA5Dataset(sst_test, pcs_test, era5_test, seq_len, pred_len)

train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

# 训练函数
def train_improved_model(model, model_name, strategy_name):
    print(f"\n开始训练改进模型: {model_name} (策略: {strategy_name})")
    
    # 优化器设置
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    criterion = nn.MSELoss()
    
    # 学习率调度器 - 更激进的衰减
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.3, patience=3, verbose=True, min_lr=1e-6
    )
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        
        for batch_idx, (inputs, targets) in enumerate(train_pbar):
            inputs, targets = inputs.to(device), targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)
            
            optimizer.step()
            train_loss += loss.item()
            
            train_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for inputs, targets in val_pbar:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
                val_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_val_loss = val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  训练损失: {avg_train_loss:.6f}')
        print(f'  验证损失: {avg_val_loss:.6f}')
        print(f'  当前学习率: {optimizer.param_groups[0]["lr"]:.2e}')
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            torch.save(model.state_dict(), f'improved_sst_pcs_era5_results/best_{model_name}.pth')
            print(f'  保存最佳模型 (验证损失: {best_val_loss:.6f})')
        else:
            patience_counter += 1
            print(f'  早停计数: {patience_counter}/{patience}')
        
        if patience_counter >= patience:
            print(f'早停触发，停止训练')
            break
    
    return train_losses, val_losses

# 测试函数
def test_improved_model(model, model_name):
    print(f"\n测试改进模型: {model_name}")
    
    model.load_state_dict(torch.load(f'improved_sst_pcs_era5_results/best_{model_name}.pth'))
    model.eval()
    
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        for inputs, targets in tqdm(test_loader, desc=f'测试 {model_name}'):
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            
            test_predictions.append(outputs.cpu().numpy())
            test_targets.append(targets.cpu().numpy())
    
    test_predictions = np.concatenate(test_predictions, axis=0)
    test_targets = np.concatenate(test_targets, axis=0)
    
    # 计算评估指标
    mse = mean_squared_error(test_targets.flatten(), test_predictions.flatten())
    mae = mean_absolute_error(test_targets.flatten(), test_predictions.flatten())
    rmse = np.sqrt(mse)
    
    print(f"{model_name} 测试结果:")
    print(f"  MSE: {mse:.6f}")
    print(f"  MAE: {mae:.6f}")
    print(f"  RMSE: {rmse:.6f}")
    
    # 保存结果
    np.save(f'improved_sst_pcs_era5_results/test_predictions_{model_name}.npy', test_predictions)
    np.save(f'improved_sst_pcs_era5_results/test_targets_{model_name}.npy', test_targets)
    
    return {'mse': mse, 'mae': mae, 'rmse': rmse}

# 训练不同改进策略的模型
improvement_strategies = [
    ("selective_pcs", "选择性PCs"),
    ("weighted_fusion", "加权融合"),
    ("hierarchical_fusion", "分层融合")
]

results = {}

for strategy, strategy_name in improvement_strategies:
    print(f"\n{'='*60}")
    print(f"训练改进策略: {strategy_name}")
    print(f"{'='*60}")
    
    # 创建改进模型
    model = ImprovedSSTWithPCsAndERA5ConvLSTMModel(
        num_pcs=10,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=hidden_dim,
        num_layers=num_layers,
        dropout=dropout,
        improvement_strategy=strategy
    ).to(device)
    
    model_name = f"improved_{strategy}"
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练模型
    train_losses, val_losses = train_improved_model(model, model_name, strategy_name)
    
    # 测试模型
    test_results = test_improved_model(model, model_name)
    results[strategy] = test_results
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='训练损失', color='blue')
    plt.plot(val_losses, label='验证损失', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.title(f'{strategy_name} 改进模型训练曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'improved_sst_pcs_era5_figures/training_curves_{model_name}.png', 
               dpi=300, bbox_inches='tight')
    plt.close()

# 生成改进结果对比报告
print(f"\n{'='*60}")
print("改进模型结果对比")
print(f"{'='*60}")

comparison_data = []
for strategy, strategy_name in improvement_strategies:
    if strategy in results:
        result = results[strategy]
        comparison_data.append({
            'strategy': strategy_name,
            'rmse': result['rmse'],
            'mae': result['mae']
        })
        print(f"{strategy_name}:")
        print(f"  RMSE: {result['rmse']:.6f}")
        print(f"  MAE: {result['mae']:.6f}")

# 保存对比结果
import pandas as pd
df_comparison = pd.DataFrame(comparison_data)
df_comparison.to_csv('improved_sst_pcs_era5_results/improvement_comparison.csv', index=False)

print(f"\n改进模型训练完成！")
print(f"结果保存在 improved_sst_pcs_era5_results/ 文件夹中")
