import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import os
import time
import xarray as xr
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from tqdm import tqdm

print("开始基于SST+PCs ConvLSTM模型的直接SST预测训练...")

# 创建结果文件夹
os.makedirs('sst_pcs_convlstm_results', exist_ok=True)
os.makedirs('sst_pcs_convlstm_figures', exist_ok=True)

# 设置随机种子确保可重复性
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载原始SST数据
print("加载原始SST数据...")
sst_data = xr.open_dataset('SST-V2.nc')
sst_values = sst_data.analysed_sst.values  # 形状: (time, lat, lon)

# 获取空间维度
n_time_total, n_lat, n_lon = sst_values.shape
print(f"SST数据形状: {sst_values.shape}")

# 数据集划分（与EOF分解保持一致：7:2:1）
train_end = int(n_time_total * 0.7)
val_end = int(n_time_total * 0.9)

sst_train = sst_values[:train_end]
sst_val = sst_values[train_end:val_end]
sst_test = sst_values[val_end:]

print(f"SST训练集形状: {sst_train.shape}")
print(f"SST验证集形状: {sst_val.shape}")
print(f"SST测试集形状: {sst_test.shape}")

# 加载EOF分解后的PCs数据
print("加载EOF分解后的PCs数据...")
PCs_train = np.load('results/PCs_train.npy')  # 形状: (k, train_len)
PCs_val = np.load('results/PCs_val.npy')      # 形状: (k, val_len)
PCs_test = np.load('results/PCs_test.npy')    # 形状: (k, test_len)

k, train_len = PCs_train.shape
_, val_len = PCs_val.shape
_, test_len = PCs_test.shape

print(f"PCs特征个数 k: {k}")
print(f"PCs训练集长度: {train_len}")
print(f"PCs验证集长度: {val_len}")
print(f"PCs测试集长度: {test_len}")

# 加载标准化参数
print("加载SST标准化参数...")
sst_mean = np.load('results/sst_mean.npy')
sst_std = np.load('results/sst_std.npy')

# SST数据标准化处理
print("对SST数据进行标准化处理...")
# 使用训练集计算标准化参数
sst_train_norm = (sst_train - sst_mean) / sst_std
sst_val_norm = (sst_val - sst_mean) / sst_std
sst_test_norm = (sst_test - sst_mean) / sst_std

print("标准化后SST范围:")
print(f"训练集: {sst_train_norm.min():.4f} - {sst_train_norm.max():.4f}")
print(f"验证集: {sst_val_norm.min():.4f} - {sst_val_norm.max():.4f}")
print(f"测试集: {sst_test_norm.min():.4f} - {sst_test_norm.max():.4f}")

# PCs数据标准化处理
print("对PCs数据进行标准化处理...")
pc_scaler = StandardScaler()
# 转置以便按特征进行标准化
PCs_train_flat = PCs_train.T
# 使用训练集拟合scaler
pc_scaler.fit(PCs_train_flat)
# 变换所有数据集
PCs_train_scaled = pc_scaler.transform(PCs_train_flat).T
PCs_val_scaled = pc_scaler.transform(PCs_val.T).T
PCs_test_scaled = pc_scaler.transform(PCs_test.T).T

# 保存scaler供后续使用
np.save('sst_pcs_convlstm_results/pc_scaler_mean.npy', pc_scaler.mean_)
np.save('sst_pcs_convlstm_results/pc_scaler_scale.npy', pc_scaler.scale_)

print("标准化后PCs范围:")
print(f"训练集: {PCs_train_scaled.min():.4f} - {PCs_train_scaled.max():.4f}")
print(f"验证集: {PCs_val_scaled.min():.4f} - {PCs_val_scaled.max():.4f}")
print(f"测试集: {PCs_test_scaled.min():.4f} - {PCs_test_scaled.max():.4f}")

# 定义SST+PCs联合数据集类（优化版本）
class SSTWithPCsDataset(Dataset):
    def __init__(self, sst_data, pcs_data, seq_len, pred_len):
        """
        sst_data: 形状 (time, lat, lon)
        pcs_data: 形状 (k, time)
        """
        self.sst_data = sst_data
        self.pcs_data = pcs_data
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.total_len = seq_len + pred_len

        # 获取空间维度
        self.n_lat, self.n_lon = sst_data.shape[1], sst_data.shape[2]
        self.k = pcs_data.shape[0]

        # 确保时间维度匹配
        assert sst_data.shape[0] == pcs_data.shape[1], f"SST和PCs的时间维度不匹配: {sst_data.shape[0]} vs {pcs_data.shape[1]}"

        print(f"📊 数据集信息:")
        print(f"  SST形状: {sst_data.shape}")
        print(f"  PCs形状: {pcs_data.shape}")
        print(f"  序列长度: {seq_len}, 预测长度: {pred_len}")
        print(f"  样本数量: {len(self)}")

    def __len__(self):
        return self.sst_data.shape[0] - self.total_len + 1

    def __getitem__(self, idx):
        # SST输入序列: (seq_len, lat, lon)
        sst_x = self.sst_data[idx:idx+self.seq_len]
        # SST目标: (pred_len, lat, lon)
        sst_y = self.sst_data[idx+self.seq_len:idx+self.total_len]

        # PCs输入序列: (seq_len, k)
        pcs_x = self.pcs_data[:, idx:idx+self.seq_len].T

        # 优化的PCs空间扩展方法
        # 方法1: 使用numpy.tile代替broadcast_to（更内存友好）
        pcs_spatial = np.tile(
            pcs_x[:, :, np.newaxis, np.newaxis],
            (1, 1, self.n_lat, self.n_lon)
        )

        # 合并SST和PCs: (seq_len, 1+k, lat, lon)
        # SST作为第一个通道，PCs作为后续通道
        sst_expanded = sst_x[:, np.newaxis, :, :]  # (seq_len, 1, lat, lon)
        combined_input = np.concatenate([sst_expanded, pcs_spatial], axis=1)

        # 数据类型转换和形状检查
        if combined_input.shape != (self.seq_len, 1 + self.k, self.n_lat, self.n_lon):
            raise ValueError(f"输入形状错误: 期望 {(self.seq_len, 1 + self.k, self.n_lat, self.n_lon)}, 实际 {combined_input.shape}")

        if sst_y.shape != (self.pred_len, self.n_lat, self.n_lon):
            raise ValueError(f"目标形状错误: 期望 {(self.pred_len, self.n_lat, self.n_lon)}, 实际 {sst_y.shape}")

        return torch.FloatTensor(combined_input), torch.FloatTensor(sst_y)


# 内存优化的数据集类（备选方案）
class MemoryEfficientSSTWithPCsDataset(Dataset):
    def __init__(self, sst_data, pcs_data, seq_len, pred_len):
        """
        内存优化版本：不预先广播PCs，在需要时动态生成
        """
        self.sst_data = sst_data
        self.pcs_data = pcs_data
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.total_len = seq_len + pred_len

        self.n_lat, self.n_lon = sst_data.shape[1], sst_data.shape[2]
        self.k = pcs_data.shape[0]

        assert sst_data.shape[0] == pcs_data.shape[1], f"时间维度不匹配: {sst_data.shape[0]} vs {pcs_data.shape[1]}"

    def __len__(self):
        return self.sst_data.shape[0] - self.total_len + 1

    def __getitem__(self, idx):
        # SST数据
        sst_x = self.sst_data[idx:idx+self.seq_len]
        sst_y = self.sst_data[idx+self.seq_len:idx+self.total_len]

        # PCs数据
        pcs_x = self.pcs_data[:, idx:idx+self.seq_len].T  # (seq_len, k)

        # 动态生成PCs空间表示（减少内存使用）
        # 只在需要时创建空间维度
        combined_input = np.zeros((self.seq_len, 1 + self.k, self.n_lat, self.n_lon), dtype=np.float32)

        # 填充SST通道
        combined_input[:, 0, :, :] = sst_x

        # 填充PCs通道（广播到空间维度）
        for t in range(self.seq_len):
            for k in range(self.k):
                combined_input[t, 1+k, :, :] = pcs_x[t, k]

        return torch.FloatTensor(combined_input), torch.FloatTensor(sst_y)

# 滑动窗口参数
seq_len = 14  # 14天的输入序列
pred_len = 1   # 预测未来1天

# 创建SST+PCs联合数据集
print("\n📦 创建数据集...")

# 选择数据集类型（可以根据内存情况选择）
use_memory_efficient = False  # 如果内存不足，设置为True

try:
    if use_memory_efficient:
        print("使用内存优化版本数据集")
        train_dataset = MemoryEfficientSSTWithPCsDataset(sst_train_norm, PCs_train_scaled, seq_len, pred_len)
        val_dataset = MemoryEfficientSSTWithPCsDataset(sst_val_norm, PCs_val_scaled, seq_len, pred_len)
        test_dataset = MemoryEfficientSSTWithPCsDataset(sst_test_norm, PCs_test_scaled, seq_len, pred_len)
    else:
        print("使用标准版本数据集")
        train_dataset = SSTWithPCsDataset(sst_train_norm, PCs_train_scaled, seq_len, pred_len)
        val_dataset = SSTWithPCsDataset(sst_val_norm, PCs_val_scaled, seq_len, pred_len)
        test_dataset = SSTWithPCsDataset(sst_test_norm, PCs_test_scaled, seq_len, pred_len)

    print("✅ 数据集创建成功")

except Exception as e:
    print(f"❌ 数据集创建失败: {e}")
    print("🔄 尝试使用内存优化版本...")
    train_dataset = MemoryEfficientSSTWithPCsDataset(sst_train_norm, PCs_train_scaled, seq_len, pred_len)
    val_dataset = MemoryEfficientSSTWithPCsDataset(sst_val_norm, PCs_val_scaled, seq_len, pred_len)
    test_dataset = MemoryEfficientSSTWithPCsDataset(sst_test_norm, PCs_test_scaled, seq_len, pred_len)
    print("✅ 内存优化版本数据集创建成功")

# 检查数据形状和内容
print("\n🔍 数据形状检查...")
try:
    sample_input, sample_target = train_dataset[0]
    print(f"✅ 输入数据形状: {sample_input.shape}")  # (seq_len, 1+k, lat, lon)
    print(f"✅ 目标数据形状: {sample_target.shape}")  # (pred_len, lat, lon)

    # 检查数据范围
    print(f"📊 输入数据范围: {sample_input.min():.4f} - {sample_input.max():.4f}")
    print(f"📊 目标数据范围: {sample_target.min():.4f} - {sample_target.max():.4f}")

    # 检查是否有NaN或Inf
    if torch.isnan(sample_input).any() or torch.isinf(sample_input).any():
        print("⚠️ 警告: 输入数据包含NaN或Inf值")
    if torch.isnan(sample_target).any() or torch.isinf(sample_target).any():
        print("⚠️ 警告: 目标数据包含NaN或Inf值")

except Exception as e:
    print(f"❌ 数据形状检查失败: {e}")
    raise

# 动态调整批次大小
expected_memory_per_sample = sample_input.numel() * 4 / (1024**2)  # MB
print(f"📊 每样本预估内存: {expected_memory_per_sample:.2f} MB")

if expected_memory_per_sample > 50:  # 如果每样本超过50MB
    batch_size = max(1, 16 // int(expected_memory_per_sample / 50))
    print(f"⚠️ 自动调整批次大小为: {batch_size}")
else:
    batch_size = 16

# 创建数据加载器
print(f"\n📦 创建数据加载器 (batch_size={batch_size})...")
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0, pin_memory=False)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0, pin_memory=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0, pin_memory=False)

print(f"✅ 数据加载器创建完成")
print(f"📊 训练样本数: {len(train_dataset)}")
print(f"📊 验证样本数: {len(val_dataset)}")
print(f"📊 测试样本数: {len(test_dataset)}")
print(f"📊 训练批次数: {len(train_loader)}")
print(f"📊 验证批次数: {len(val_loader)}")
print(f"📊 测试批次数: {len(test_loader)}")

# 导入新的模型
from eof_convlstm_model import SSTWithPCsConvLSTMModel, MultiScaleSSTWithPCsConvLSTMModel

# 选择模型类型
model_type = "basic"  # 可选: "basic" 或 "multiscale"

if model_type == "basic":
    # 基础SST+PCs ConvLSTM模型
    model = SSTWithPCsConvLSTMModel(
        num_pcs=k,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=64,
        kernel_size=(3, 3),
        num_layers=2,
        dropout=0.1
    ).to(device)
    print("使用基础SST+PCs ConvLSTM模型")

elif model_type == "multiscale":
    # 多尺度SST+PCs ConvLSTM模型
    model = MultiScaleSSTWithPCsConvLSTMModel(
        num_pcs=k,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=64,
        kernel_size=(3, 3),
        num_layers=2,
        dropout=0.1
    ).to(device)
    print("使用多尺度SST+PCs ConvLSTM模型")

# 打印模型结构
print("模型结构:")
print(model)
total_params = sum(p.numel() for p in model.parameters())
print(f"模型总参数量: {total_params}")

# 优化器和损失函数
criterion = nn.MSELoss()
optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
scheduler = optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, mode='min', factor=0.5, patience=5, verbose=True, min_lr=1e-6
)

# 训练函数 - 添加进度条
def train_epoch(model, train_loader, criterion, optimizer, device, clip_grad=1.0, epoch=None):
    model.train()
    total_loss = 0

    # 创建训练进度条
    train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1} [Train]' if epoch is not None else 'Training',
                     leave=False, ncols=100)

    for batch_idx, (data, target) in enumerate(train_pbar):
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)

        optimizer.step()
        total_loss += loss.item()

        # 更新进度条显示当前损失
        current_avg_loss = total_loss / (batch_idx + 1)
        train_pbar.set_postfix({'Loss': f'{current_avg_loss:.6f}'})

    train_pbar.close()
    return total_loss / len(train_loader)

# 验证函数 - 添加进度条
def validate(model, val_loader, criterion, device, epoch=None):
    model.eval()
    total_loss = 0

    # 创建验证进度条
    val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1} [Val]' if epoch is not None else 'Validating',
                   leave=False, ncols=100)

    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(val_pbar):
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            total_loss += loss.item()

            # 更新进度条显示当前损失
            current_avg_loss = total_loss / (batch_idx + 1)
            val_pbar.set_postfix({'Loss': f'{current_avg_loss:.6f}'})

    val_pbar.close()
    return total_loss / len(val_loader)

# 测试函数 - 修改为SST预测，添加进度条
def test(model, test_loader, device):
    model.eval()
    all_preds = []
    all_targets = []

    # 创建测试进度条
    test_pbar = tqdm(test_loader, desc='Testing', ncols=100)

    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(test_pbar):
            data = data.to(device)
            output = model(data)
            all_preds.append(output.cpu().numpy())
            all_targets.append(target.numpy())

            # 更新进度条
            test_pbar.set_postfix({'Batch': f'{batch_idx+1}/{len(test_loader)}'})

    test_pbar.close()

    # 将预测和目标转换为numpy数组
    all_preds = np.concatenate(all_preds, axis=0)  # (samples, pred_len, lat, lon)
    all_targets = np.concatenate(all_targets, axis=0)  # (samples, pred_len, lat, lon)

    # 计算标准化空间中的MSE和MAE
    scaled_mse = mean_squared_error(all_targets.reshape(-1), all_preds.reshape(-1))
    scaled_mae = mean_absolute_error(all_targets.reshape(-1), all_preds.reshape(-1))

    return scaled_mse, scaled_mae, all_preds, all_targets

# SST反标准化函数
def inverse_transform_sst(scaled_sst, sst_mean, sst_std):
    """
    将标准化的SST转换回原始尺度
    scaled_sst: (samples, pred_len, lat, lon)
    """
    return scaled_sst * sst_std + sst_mean

# 模型超参数
num_epochs = 40
patience = 10
clip_grad = 1.0

# 训练模型
print("开始训练EOF-ConvLSTM模型...")
print(f"模型类型: {model_type}")
print(f"总训练轮数: {num_epochs}")
print(f"早停耐心值: {patience}")
print("-" * 80)

start_time = time.time()

train_losses = []
val_losses = []
best_val_loss = float('inf')
early_stop_counter = 0
best_model_path = f'sst_pcs_convlstm_results/best_sst_pcs_convlstm_{model_type}.pth'

# 创建总体训练进度条
epoch_pbar = tqdm(range(num_epochs), desc='Training Progress', ncols=120)

for epoch in epoch_pbar:
    # 训练
    train_loss = train_epoch(model, train_loader, criterion, optimizer, device, clip_grad, epoch)
    train_losses.append(train_loss)

    # 验证
    val_loss = validate(model, val_loader, criterion, device, epoch)
    val_losses.append(val_loss)

    # 更新学习率
    old_lr = optimizer.param_groups[0]['lr']
    scheduler.step(val_loss)
    new_lr = optimizer.param_groups[0]['lr']

    # 保存最佳模型
    is_best = False
    if val_loss < best_val_loss:
        best_val_loss = val_loss
        torch.save(model.state_dict(), best_model_path)
        early_stop_counter = 0
        is_best = True
    else:
        early_stop_counter += 1

    # 更新总体进度条信息
    postfix_dict = {
        'Train Loss': f'{train_loss:.6f}',
        'Val Loss': f'{val_loss:.6f}',
        'Best Val': f'{best_val_loss:.6f}',
        'LR': f'{new_lr:.2e}',
        'Early Stop': f'{early_stop_counter}/{patience}'
    }

    if is_best:
        postfix_dict['Status'] = '🌟 Best!'
    elif early_stop_counter > patience // 2:
        postfix_dict['Status'] = '⚠️ Warning'
    else:
        postfix_dict['Status'] = '✅ Good'

    epoch_pbar.set_postfix(postfix_dict)

    # 学习率变化提示
    if new_lr != old_lr:
        tqdm.write(f"Epoch {epoch+1}: 学习率调整 {old_lr:.2e} → {new_lr:.2e}")

    # 最佳模型保存提示
    if is_best:
        tqdm.write(f"Epoch {epoch+1}: 🌟 保存新的最佳模型! 验证损失: {val_loss:.6f}")

    # 早停检查
    if early_stop_counter >= patience:
        tqdm.write(f"Epoch {epoch+1}: ⏹️ 早停触发，停止训练")
        break

epoch_pbar.close()

training_time = time.time() - start_time
print(f"\n{'='*80}")
print(f"✅ 训练完成！")
print(f"📊 总用时: {training_time:.2f}秒 ({training_time/60:.1f}分钟)")
print(f"🏆 最佳验证损失: {best_val_loss:.6f}")
print(f"📈 训练轮数: {epoch+1}/{num_epochs}")
print(f"{'='*80}")

# 加载最佳模型并在测试集上评估
print("\n🔄 加载最佳模型进行测试...")
model.load_state_dict(torch.load(best_model_path))

print("📊 开始测试集评估...")
test_mse, test_mae, test_preds, test_targets = test(model, test_loader, device)

print(f"\n📈 测试集结果 (标准化尺度):")
print(f"  MSE: {test_mse:.6f}")
print(f"  MAE: {test_mae:.6f}")

# 反标准化预测结果和真实值
print("\n🔄 反标准化测试集预测结果...")
with tqdm(total=3, desc='数据处理', ncols=100) as pbar:
    pbar.set_description('反标准化预测结果')
    inverse_preds = inverse_transform_sst(test_preds, sst_mean, sst_std)
    pbar.update(1)

    pbar.set_description('反标准化真实值')
    inverse_targets = inverse_transform_sst(test_targets, sst_mean, sst_std)
    pbar.update(1)

    pbar.set_description('计算误差统计')
    # 计算反标准化后的误差
    inverse_mse = mean_squared_error(inverse_targets.reshape(-1), inverse_preds.reshape(-1))
    inverse_mae = mean_absolute_error(inverse_targets.reshape(-1), inverse_preds.reshape(-1))
    pbar.update(1)

print(f"\n📈 测试集结果 (原始尺度):")
print(f"  MSE: {inverse_mse:.6f}")
print(f"  MAE: {inverse_mae:.6f}")

# 将开尔文转换为摄氏度
print("\n🌡️ 转换为摄氏度...")
inverse_preds_celsius = inverse_preds - 273.15
inverse_targets_celsius = inverse_targets - 273.15

# 计算摄氏度误差
celsius_rmse = np.sqrt(mean_squared_error(inverse_targets_celsius.reshape(-1), inverse_preds_celsius.reshape(-1)))
celsius_mae = mean_absolute_error(inverse_targets_celsius.reshape(-1), inverse_preds_celsius.reshape(-1))

print(f"\n🎯 最终测试集结果 (摄氏度):")
print(f"  RMSE: {celsius_rmse:.4f}°C")
print(f"  MAE:  {celsius_mae:.4f}°C")

# 保存测试结果
print("\n💾 保存测试结果...")
save_files = [
    (f'sst_pcs_convlstm_results/test_predictions_{model_type}.npy', inverse_preds_celsius, '预测结果(摄氏度)'),
    (f'sst_pcs_convlstm_results/test_targets_{model_type}.npy', inverse_targets_celsius, '真实值(摄氏度)'),
    (f'sst_pcs_convlstm_results/test_predictions_scaled_{model_type}.npy', test_preds, '预测结果(标准化)'),
    (f'sst_pcs_convlstm_results/test_targets_scaled_{model_type}.npy', test_targets, '真实值(标准化)')
]

for filepath, data, desc in tqdm(save_files, desc='保存文件', ncols=100):
    np.save(filepath, data)
    tqdm.write(f"  ✅ 已保存: {desc}")

# 绘制训练和验证损失曲线
print("\n📊 生成训练曲线图...")
with tqdm(total=1, desc='绘制训练曲线', ncols=100) as pbar:
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='训练损失', linewidth=2)
    plt.plot(val_losses, label='验证损失', linewidth=2)
    plt.xlabel('轮次', fontsize=12)
    plt.ylabel('损失', fontsize=12)
    plt.title(f'SST+PCs ConvLSTM ({model_type}) 训练和验证损失曲线', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)

    # 标记最佳点
    best_epoch = np.argmin(val_losses)
    plt.scatter(best_epoch, val_losses[best_epoch], color='red', s=100, zorder=5)
    plt.annotate(f'最佳: Epoch {best_epoch+1}\nVal Loss: {val_losses[best_epoch]:.6f}',
                xy=(best_epoch, val_losses[best_epoch]),
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

    plt.tight_layout()
    plt.savefig(f'sst_pcs_convlstm_figures/training_curves_{model_type}.png', dpi=300, bbox_inches='tight')
    plt.close()
    pbar.update(1)

# 绘制SST预测结果的空间平均时间序列
print("\n📈 生成空间平均时间序列图...")
with tqdm(total=1, desc='空间平均时间序列', ncols=100) as pbar:
    plt.figure(figsize=(12, 6))
    time_steps = range(len(test_preds))

    # 计算空间平均SST
    true_sst_mean = np.mean(inverse_targets_celsius[:, 0, :, :], axis=(1, 2))
    pred_sst_mean = np.mean(inverse_preds_celsius[:, 0, :, :], axis=(1, 2))

    plt.plot(time_steps, true_sst_mean, label='真实SST空间平均', color='blue', linewidth=2)
    plt.plot(time_steps, pred_sst_mean, label='预测SST空间平均', color='red', alpha=0.7, linewidth=2)

    # 计算相关系数
    correlation = np.corrcoef(true_sst_mean, pred_sst_mean)[0, 1]

    plt.xlabel('时间步', fontsize=12)
    plt.ylabel('SST (°C)', fontsize=12)
    plt.title(f'SST+PCs ConvLSTM ({model_type}) 空间平均SST预测结果\n相关系数: {correlation:.4f}', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'sst_pcs_convlstm_figures/sst_spatial_mean_{model_type}.png', dpi=300, bbox_inches='tight')
    plt.close()
    pbar.update(1)

# 绘制SST预测误差的空间分布（时间平均）
print("\n🗺️ 生成空间误差分布图...")
visualization_tasks = [
    ('误差分布', 'sst_error_spatial'),
    ('RMSE分布', 'sst_rmse_spatial')
]

with tqdm(visualization_tasks, desc='空间分布图', ncols=100) as pbar:
    error_celsius = inverse_preds_celsius - inverse_targets_celsius

    for task_name, filename_prefix in pbar:
        pbar.set_description(f'生成{task_name}图')

        plt.figure(figsize=(12, 8))

        if task_name == '误差分布':
            data = np.mean(error_celsius[:, 0, :, :], axis=0)  # 时间平均误差
            cmap = 'RdBu_r'
            label = '预测误差 (°C)'
            title = f'SST+PCs ConvLSTM ({model_type}) 时间平均预测误差分布'
        else:  # RMSE分布
            data = np.sqrt(np.mean(error_celsius[:, 0, :, :] ** 2, axis=0))
            cmap = 'YlOrRd'
            label = 'RMSE (°C)'
            title = f'SST+PCs ConvLSTM ({model_type}) RMSE空间分布'

        # 使用原始数据的经纬度信息
        im = plt.imshow(data, cmap=cmap,
                       extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                              sst_data.latitude.values.min(), sst_data.latitude.values.max()],
                       origin='lower')

        cbar = plt.colorbar(im, label=label)
        cbar.ax.tick_params(labelsize=10)
        plt.xlabel('经度', fontsize=12)
        plt.ylabel('纬度', fontsize=12)
        plt.title(title, fontsize=14)

        # 添加统计信息
        if task_name == '误差分布':
            stats_text = f'均值: {np.mean(data):.4f}°C\n标准差: {np.std(data):.4f}°C'
        else:
            stats_text = f'平均RMSE: {np.mean(data):.4f}°C\n最大RMSE: {np.max(data):.4f}°C'

        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        plt.savefig(f'sst_pcs_convlstm_figures/{filename_prefix}_{model_type}.png', dpi=300, bbox_inches='tight')
        plt.close()

print(f"\n{'='*80}")
print("🎉 SST+PCs ConvLSTM模型训练与评估完成！")
print(f"📁 结果保存在: sst_pcs_convlstm_results/")
print(f"📊 图表保存在: sst_pcs_convlstm_figures/")
print(f"🏆 最终测试结果: RMSE={celsius_rmse:.4f}°C, MAE={celsius_mae:.4f}°C")
print(f"{'='*80}")

# 关闭数据文件
sst_data.close()
