
# SST+ERA5 ConvLSTM消融实验总结

## 实验目的
量化PCs特征对SST预测性能的贡献

## 实验设计
- **完整模型**: SST + PCs + ERA5 → SST
- **消融模型**: SST + ERA5 → SST (移除PCs)
- **输入通道数**: 5个 (1个SST + 4个ERA5，移除了10个PCs)

## 模型对比
| 特征 | 完整模型 | 消融模型 |
|------|----------|----------|
| 输入通道 | 15 (SST+PCs+ERA5) | 5 (SST+ERA5) |
| PCs特征 | ✓ | ✗ |
| ERA5特征 | ✓ | ✓ |
| SST特征 | ✓ | ✓ |

## 结果文件
- 预测结果: sst_era5_ablation_results/
- 可视化图表: sst_era5_ablation_visualization/
- NetCDF文件: sst_era5_ablation_visualization/sst_prediction_results_*.nc

## 分析说明
通过对比完整模型和消融模型的性能差异，可以量化PCs特征的贡献：
- 性能下降 = 完整模型性能 - 消融模型性能
- PCs贡献度 = 性能下降 / 完整模型性能 × 100%

生成时间: 2025-07-03 13:45:01
