import xarray as xr
import numpy as np

# 输出重定向到文件
import sys
with open('sst_data_info.txt', 'w') as f:
    sys.stdout = f
    
    # 读取SST数据
    print("读取SST数据...")
    sst_data = xr.open_dataset('SST-V2.nc')
    print("\nSST数据结构:")
    print(sst_data)
    print("\nSST数据维度:")
    for dim_name, dim_size in sst_data.dims.items():
        print(f"{dim_name}: {dim_size}")

    # 查看变量
    print("\nSST数据变量:")
    for var_name, var in sst_data.variables.items():
        print(f"{var_name}: {var.dims}, 形状: {var.shape}")

    # 查看时间范围
    if 'time' in sst_data.dims:
        print("\n时间范围:")
        print(f"开始时间: {sst_data.time.values[0]}")
        print(f"结束时间: {sst_data.time.values[-1]}")
        print(f"时间点数量: {len(sst_data.time)}")
        
    # 检查SST数据的一些统计信息
    print("\nSST数据统计信息:")
    if 'analysed_sst' in sst_data:
        print(f"最小值: {sst_data.analysed_sst.min().values}")
        print(f"最大值: {sst_data.analysed_sst.max().values}")
        print(f"平均值: {sst_data.analysed_sst.mean().values}")

    # 关闭数据集
    sst_data.close()

# 恢复标准输出
sys.stdout = sys.__stdout__
print("数据信息已保存到 sst_data_info.txt") 