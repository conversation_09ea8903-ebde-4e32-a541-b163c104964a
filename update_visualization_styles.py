#!/usr/bin/env python3
"""
统一更新所有可视化脚本的样式和配置
应用统一的颜色方案、范围设置和日期显示

作者: AI Assistant
日期: 2025-07-02
"""

import os
import re
from pathlib import Path

print("🎨 开始统一更新可视化样式...")

# 需要更新的可视化脚本列表
visualization_scripts = [
    'visualize_sst_pcs_era5_convlstm.py',
    'visualize_sst_era5_convlstm.py', 
    'visualize_sst_only_convlstm.py',
    'visualize_sst_pcs_convlstm.py',
    'compare_models.py',
    'comprehensive_model_comparison.py'
]

# 更新规则
update_rules = [
    # 颜色映射更新
    (r"cmap='coolwarm'", "cmap=SST_COLORMAP"),
    (r"cmap='plasma'", "cmap=SST_COLORMAP"),
    (r"cmap='RdBu_r'", "cmap=ERROR_COLORMAP"),
    (r"cmap='viridis'", "cmap=RMSE_COLORMAP"),
    (r"cmap='YlOrRd'", "cmap=RMSE_COLORMAP"),
    
    # 颜色范围更新
    (r"vmin=20,?\s*vmax=32", "vmin=SST_VMIN, vmax=SST_VMAX"),
    (r"vmin=-2,?\s*vmax=2", "vmin=ERROR_VMIN, vmax=ERROR_VMAX"),
    (r"vmin=0,?\s*vmax=1", "vmin=RMSE_VMIN, vmax=RMSE_VMAX"),
    
    # 图表尺寸更新
    (r"figsize=\(18,\s*6\)", "figsize=FIGURE_SIZES['triple_comparison']"),
    (r"figsize=\(12,\s*8\)", "figsize=FIGURE_SIZES['single_map']"),
    (r"figsize=\(12,\s*6\)", "figsize=FIGURE_SIZES['time_series']"),
    (r"figsize=\(15,\s*10\)", "figsize=FIGURE_SIZES['performance_bar']"),
    
    # 字体大小更新
    (r"fontsize=14", "fontsize=FONT_SIZES['title']"),
    (r"fontsize=12", "fontsize=FONT_SIZES['label']"),
    (r"fontsize=11", "fontsize=FONT_SIZES['legend']"),
    (r"fontsize=10", "fontsize=FONT_SIZES['tick']"),
    
    # DPI更新
    (r"dpi=300", "dpi=DPI"),
    
    # 颜色更新
    (r"color='blue'", "color=TIMESERIES_COLORS['true']"),
    (r"color='red'", "color=TIMESERIES_COLORS['predicted']"),
]

def add_import_if_missing(content, script_name):
    """如果缺少导入语句，则添加"""
    import_block = """
# 导入统一的可视化配置
from visualization_config import (
    SST_COLORMAP, SST_VMIN, SST_VMAX,
    ERROR_COLORMAP, ERROR_VMIN, ERROR_VMAX,
    RMSE_COLORMAP, RMSE_VMIN, RMSE_VMAX,
    TIMESERIES_COLORS, FIGURE_SIZES, FONT_SIZES, DPI,
    get_sample_date_mapping, format_date_for_title,
    get_adaptive_sst_range, get_adaptive_error_range
)
"""
    
    # 检查是否已经有导入
    if 'from visualization_config import' in content:
        return content
    
    # 找到合适的位置插入导入语句
    lines = content.split('\n')
    insert_pos = -1
    
    for i, line in enumerate(lines):
        if line.strip().startswith('from tqdm import') or line.strip().startswith('import tqdm'):
            insert_pos = i + 1
            break
        elif line.strip().startswith('print(') and '可视化' in line:
            insert_pos = i
            break
    
    if insert_pos > 0:
        lines.insert(insert_pos, import_block)
        return '\n'.join(lines)
    
    return content

def update_script(script_path):
    """更新单个脚本文件"""
    if not os.path.exists(script_path):
        print(f"⚠️  文件不存在: {script_path}")
        return False
    
    print(f"📝 更新 {script_path}...")
    
    try:
        # 读取文件内容
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 添加导入语句
        content = add_import_if_missing(content, script_path)
        
        # 应用更新规则
        for pattern, replacement in update_rules:
            content = re.sub(pattern, replacement, content)
        
        # 特殊处理：添加日期映射
        if 'sample_indices = [0, len(predictions)' in content and 'date_mapping = get_sample_date_mapping' not in content:
            # 在sample_indices定义前添加日期映射
            content = content.replace(
                '# 可视化几个时间点的SST对比\n    sample_indices = [0, len(predictions)',
                '# 获取样本日期映射\n    date_mapping = get_sample_date_mapping(len(predictions), seq_len=14)\n    \n    # 可视化几个时间点的SST对比\n    sample_indices = [0, len(predictions)'
            )
        
        # 特殊处理：更新标题中的日期
        if 'sample_date = date_mapping.get(sample_idx' not in content and 'for i, sample_idx in enumerate(sample_indices):' in content:
            content = content.replace(
                'for i, sample_idx in enumerate(sample_indices):\n        if sample_idx >= len(predictions):\n            continue\n            \n        fig, axes = plt.subplots',
                'for i, sample_idx in enumerate(sample_indices):\n        if sample_idx >= len(predictions):\n            continue\n        \n        # 获取样本日期\n        sample_date = date_mapping.get(sample_idx, f\'样本{sample_idx}\')\n            \n        fig, axes = plt.subplots'
            )
        
        # 更新标题中的样本信息
        content = re.sub(
            r"set_title\(f'([^']*)\n样本 \{sample_idx\}'",
            r"set_title(f'\1\n{sample_date}'",
            content
        )
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {script_path} 更新完成")
            return True
        else:
            print(f"ℹ️  {script_path} 无需更新")
            return False
            
    except Exception as e:
        print(f"❌ 更新 {script_path} 失败: {e}")
        return False

def main():
    """主函数"""
    updated_count = 0
    total_count = 0
    
    for script in visualization_scripts:
        total_count += 1
        if update_script(script):
            updated_count += 1
    
    print(f"\n📊 更新统计:")
    print(f"总文件数: {total_count}")
    print(f"已更新: {updated_count}")
    print(f"无需更新: {total_count - updated_count}")
    
    # 创建样式示例文件
    create_style_example()
    
    print("\n🎉 可视化样式统一更新完成！")
    print("\n📋 统一后的样式标准:")
    print("  🎨 SST场: plasma色图, 20-32°C")
    print("  🎨 误差场: RdBu_r色图, -2到+2°C")
    print("  🎨 RMSE场: YlOrRd色图, 0-1°C")
    print("  📏 图表尺寸: 标准化尺寸")
    print("  🔤 字体大小: 统一字体配置")
    print("  📅 日期显示: 自动添加样本日期")

def create_style_example():
    """创建样式示例文件"""
    example_content = """# 可视化样式统一示例

## 使用统一配置的示例代码

```python
from visualization_config import (
    SST_COLORMAP, SST_VMIN, SST_VMAX,
    ERROR_COLORMAP, ERROR_VMIN, ERROR_VMAX,
    FIGURE_SIZES, FONT_SIZES, DPI,
    get_sample_date_mapping
)

# 获取日期映射
date_mapping = get_sample_date_mapping(len(predictions), seq_len=14)
sample_date = date_mapping.get(sample_idx, f'样本{sample_idx}')

# 绘制SST场
fig, axes = plt.subplots(1, 3, figsize=FIGURE_SIZES['triple_comparison'])

im1 = axes[0].imshow(sst_data, cmap=SST_COLORMAP, 
                    vmin=SST_VMIN, vmax=SST_VMAX)
axes[0].set_title(f'真实SST (°C)\\n{sample_date}', 
                 fontsize=FONT_SIZES['title'])

# 保存图片
plt.savefig('output.png', dpi=DPI, bbox_inches='tight')
```

## 统一的颜色方案

- **SST场**: plasma色图 (20-32°C)
- **误差场**: RdBu_r色图 (-2到+2°C)  
- **RMSE场**: YlOrRd色图 (0-1°C)
- **时间序列**: 蓝色(真实值), 橙色(预测值), 红色(误差)

## 标准图表尺寸

- **三联对比图**: (18, 6)
- **单个地图**: (12, 8)
- **时间序列**: (12, 6)
- **性能对比**: (15, 10)
"""
    
    with open('visualization_style_guide.md', 'w', encoding='utf-8') as f:
        f.write(example_content)
    
    print("📖 已创建样式指南: visualization_style_guide.md")

if __name__ == "__main__":
    main()
