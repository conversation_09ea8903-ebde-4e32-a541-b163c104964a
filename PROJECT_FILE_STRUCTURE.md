# 项目文件结构详细说明

## 📁 项目概述

本项目实现了基于深度学习的海表温度(SST)预测系统，包含多种模型架构和实验方案。项目从最初的Transformer+EOF方法发展到ConvLSTM直接预测，并进行了全面的消融实验和改进策略研究。

---

## 📊 数据文件

### 原始数据
- **`SST-V2.nc`** - 海表温度原始数据文件(NetCDF格式)
- **`data_ERA5.nc`** - ERA5大气再分析数据(包含u10, v10, t2m, msl变量)

### 数据信息文件
- **`sst_data_info.txt`** - SST数据的基本信息和统计特征
- **`requirements.txt`** - Python依赖包列表

---

## 🧠 核心模型文件

### 模型定义
- **`eof_convlstm_model.py`** - 🌟 **核心模型库**，包含所有模型架构:
  - `ConvLSTM` - 基础ConvLSTM层
  - `EOFTransformerModel` - Transformer+EOF模型
  - `EOFConvLSTMModel` - EOF+ConvLSTM模型
  - `SSTWithPCsConvLSTMModel` - SST+PCs ConvLSTM模型
  - `SSTWithPCsAndERA5ConvLSTMModel` - SST+PCs+ERA5完整模型
  - `SSTWithERA5ConvLSTMModel` - SST+ERA5消融模型
  - `SSTOnlyConvLSTMModel` - 纯SST基线模型

### 改进模型
- **`improve_sst_pcs_era5_model.py`** - 改进策略模型实现:
  - 选择性PCs使用
  - 加权特征融合
  - 分层特征融合

### 历史模型文件
- **`transformer_eof_model.py`** - 原始Transformer模型实现
- **`transformer_eof_model copy.py`** - Transformer模型备份

---

## 🔧 数据预处理脚本

### 主要预处理
- **`preprocess_and_eof.py`** - 🌟 **核心预处理脚本**:
  - SST数据加载和预处理
  - EOF分解和主成分提取
  - 数据集划分(训练/验证/测试)

- **`preprocess_sst_pcs_era5.py`** - SST+PCs+ERA5数据预处理:
  - ERA5数据插值和对齐
  - 多源数据标准化
  - 特征组合和保存

### 数据分析
- **`analyze_era5_features.py`** - ERA5特征分析:
  - ERA5变量与SST相关性分析
  - 空间分布可视化
  - 时间序列对比

### 调试和检查
- **`check_nc_files.py`** - NetCDF文件检查
- **`check_pcs.py`** - 主成分数据检查
- **`check_sst_data.py`** - SST数据检查
- **`debug_data_shapes.py`** - 数据维度调试

---

## 🚀 训练脚本

### 主要训练脚本
- **`train_sst_pcs_era5_convlstm.py`** - 🌟 **SST+PCs+ERA5完整模型训练**
- **`train_sst_era5_convlstm.py`** - SST+ERA5消融实验训练
- **`train_sst_only_convlstm.py`** - 纯SST基线模型训练
- **`train_improved_sst_pcs_era5.py`** - 改进策略模型训练

### 历史训练脚本
- **`train_eof_convlstm.py`** - EOF+ConvLSTM模型训练
- **`train_convlstm.py`** - 基础ConvLSTM训练

---

## 📈 可视化脚本

### 结果可视化
- **`visualize_sst_pcs_era5_convlstm.py`** - SST+PCs+ERA5模型结果可视化
- **`visualize_sst_era5_convlstm.py`** - 消融实验结果可视化
- **`visualize_sst_only_convlstm.py`** - 纯SST模型结果可视化
- **`visualize_sst_pcs_convlstm.py`** - SST+PCs模型结果可视化

### 其他可视化
- **`visualize_sst.py`** - 基础SST数据可视化
- **`nanhai_map.py`** - 南海地图绘制

---

## 🔬 实验和分析脚本

### 模型比较
- **`compare_models.py`** - 🌟 **模型性能比较框架**
- **`comprehensive_model_comparison.py`** - 综合模型对比分析
- **`convert_to_celsius_metrics.py`** - 摄氏度指标转换和评估
- **`evaluate_improved_models.py`** - 改进模型详细评估

### 重构和验证
- **`reconstruct_sst.py`** - SST重构验证
- **`reconstruct_sst_convlstm.py`** - ConvLSTM重构验证

---

## 🧪 测试脚本

- **`test_sst_pcs_era5_model.py`** - SST+PCs+ERA5模型测试
- **`test_ablation_models.py`** - 消融实验模型测试
- **`test_sst_only_model.py`** - 纯SST模型测试

---

## 🔄 流水线脚本

### 自动化流水线
- **`run_sst_pcs_era5_pipeline.py`** - 🌟 **SST+PCs+ERA5完整流水线**
- **`run_ablation_study.py`** - 消融实验自动化流水线
- **`run_eof_convlstm_pipeline.py`** - EOF+ConvLSTM流水线

---

## 📚 文档文件

### 主要文档
- **`README.md`** - 项目主文档
- **`SST_PCs_ERA5_ConvLSTM_README.md`** - 🌟 **SST+PCs+ERA5方案详细文档**
- **`Ablation_Study_README.md`** - 消融实验设计文档
- **`PROJECT_FILE_STRUCTURE.md`** - 本文档(项目文件结构说明)

### 历史文档
- **`EOF_ConvLSTM_README.md`** - EOF+ConvLSTM方案文档
- **`SST_PCs_ConvLSTM_README.md`** - SST+PCs方案文档

---

## 📁 数据文件夹

### 预处理数据
- **`results/`** - EOF分解和基础预处理结果:
  - `EOFs_k.npy` - EOF模态
  - `PCs_*.npy` - 主成分时间序列
  - `sst_mean.npy`, `sst_std.npy` - SST标准化参数

- **`sst_pcs_era5_data/`** - 🌟 **多源数据预处理结果**:
  - SST标准化数据
  - PCs缩放数据
  - ERA5变量标准化数据

### 分析结果
- **`era5_analysis/`** - ERA5特征分析结果
- **`data_loaders/`** - 数据加载器参数

---

## 📊 结果文件夹

### 模型训练结果
- **`sst_pcs_era5_results/`** - 🌟 **完整模型训练结果**
- **`sst_era5_ablation_results/`** - 消融实验结果
- **`sst_only_results/`** - 纯SST基线模型结果
- **`improved_sst_pcs_era5_results/`** - 改进模型结果

### 历史结果
- **`model_results/`** - Transformer模型结果
- **`eof_convlstm_results/`** - EOF+ConvLSTM结果
- **`sst_pcs_convlstm_results/`** - SST+PCs模型结果

---

## 🖼️ 图表文件夹

### 训练图表
- **`sst_pcs_era5_figures/`** - 完整模型训练曲线
- **`sst_era5_ablation_figures/`** - 消融实验训练图表
- **`improved_sst_pcs_era5_figures/`** - 改进模型训练图表

### 可视化结果
- **`sst_pcs_era5_visualization/`** - 🌟 **完整模型可视化结果**
- **`sst_era5_ablation_visualization/`** - 消融实验可视化
- **`celsius_evaluation_results/`** - 摄氏度评估结果
- **`improved_model_evaluation/`** - 改进模型评估图表

### 比较分析
- **`model_comparison/`** - 🌟 **模型性能比较图表**

---

## 🔧 系统文件

- **`__pycache__/`** - Python缓存文件
- **`logs/`** - 运行日志
- **`models/`** - 历史模型权重文件

---

## 🎯 核心文件推荐

### 🌟 最重要的文件 (必读)
1. **`eof_convlstm_model.py`** - 所有模型架构定义
2. **`train_sst_pcs_era5_convlstm.py`** - 主要训练脚本
3. **`run_sst_pcs_era5_pipeline.py`** - 完整自动化流水线
4. **`SST_PCs_ERA5_ConvLSTM_README.md`** - 技术方案文档
5. **`compare_models.py`** - 模型性能比较

### 📊 数据和结果 (重要)
1. **`sst_pcs_era5_data/`** - 预处理数据
2. **`sst_pcs_era5_results/`** - 训练结果
3. **`model_comparison/`** - 比较分析结果

### 🔬 实验扩展 (进阶)
1. **`run_ablation_study.py`** - 消融实验
2. **`comprehensive_model_comparison.py`** - 综合对比
3. **`improve_sst_pcs_era5_model.py`** - 改进策略

---

## 🔄 项目发展历程

### 第一阶段: Transformer+EOF方案
- **目标**: 使用Transformer预测主成分，再重构SST
- **核心文件**: `transformer_eof_model.py`, `train_eof_convlstm.py`
- **结果**: 实现了基础功能，但重构误差较大

### 第二阶段: ConvLSTM直接预测
- **目标**: 使用ConvLSTM直接预测SST，避免重构误差
- **核心文件**: `train_sst_pcs_convlstm.py`, `visualize_sst_pcs_convlstm.py`
- **结果**: 性能显著提升，RMSE降至0.26°C左右

### 第三阶段: 多源数据融合
- **目标**: 融合ERA5大气数据，提升预测精度
- **核心文件**: `preprocess_sst_pcs_era5.py`, `train_sst_pcs_era5_convlstm.py`
- **结果**: 实现了SST+PCs+ERA5多源数据融合

### 第四阶段: 消融实验和改进
- **目标**: 量化不同特征的贡献，优化模型性能
- **核心文件**: `run_ablation_study.py`, `train_improved_sst_pcs_era5.py`
- **结果**: 发现消融模型(SST+ERA5)性能最佳

---

## 📋 使用指南

### 🚀 快速开始
```bash
# 1. 数据预处理
python preprocess_sst_pcs_era5.py

# 2. 训练完整模型
python train_sst_pcs_era5_convlstm.py

# 3. 可视化结果
python visualize_sst_pcs_era5_convlstm.py

# 4. 模型比较
python compare_models.py
```

### 🔬 完整实验流程
```bash
# 运行完整流水线
python run_sst_pcs_era5_pipeline.py --model-type both

# 运行消融实验
python run_ablation_study.py --model-type basic

# 综合模型比较
python comprehensive_model_comparison.py
```

### 🧪 单独测试
```bash
# 测试模型架构
python test_sst_pcs_era5_model.py

# 测试消融模型
python test_ablation_models.py

# 测试纯SST基线
python test_sst_only_model.py
```

---

## 📊 数据流向图

```
原始数据 (SST-V2.nc, data_ERA5.nc)
    ↓
预处理 (preprocess_sst_pcs_era5.py)
    ↓
标准化数据 (sst_pcs_era5_data/)
    ↓
模型训练 (train_*.py)
    ↓
训练结果 (*_results/)
    ↓
可视化分析 (visualize_*.py)
    ↓
最终图表 (*_visualization/)
```

---

## 🎯 模型架构对比

| 模型类型 | 输入特征 | 通道数 | 参数量 | 性能(RMSE) |
|----------|----------|--------|--------|------------|
| 纯SST基线 | SST | 1 | ~468K | 待测试 |
| SST+PCs | SST+PCs | 11 | ~485K | ~0.264°C |
| SST+ERA5(消融) | SST+ERA5 | 5 | ~478K | **0.253°C** |
| SST+PCs+ERA5(完整) | SST+PCs+ERA5 | 15 | ~501K | 0.257°C |
| 改进模型 | 优化特征 | 5-15 | ~478-501K | 0.253-0.255°C |

---

## 🔧 故障排除

### 常见问题
1. **CUDA内存不足**: 减少batch_size或hidden_dim
2. **数据文件缺失**: 先运行预处理脚本
3. **模型加载失败**: 检查模型文件路径
4. **可视化错误**: 确保有训练结果文件

### 调试工具
- `debug_data_shapes.py` - 检查数据维度
- `check_*.py` - 验证数据完整性
- `test_*.py` - 测试模型架构

---

## 📈 性能基准

### 最佳性能模型
- **模型**: SST+ERA5 ConvLSTM (消融模型)
- **RMSE**: 0.2526°C
- **MAE**: 0.1914°C
- **特点**: 简单高效，性能最佳

### 推荐配置
- **序列长度**: 14天
- **预测长度**: 1天
- **隐藏维度**: 64
- **批次大小**: 8
- **学习率**: 0.001

---

*本文档提供了项目的完整文件结构说明和使用指南。建议从核心文件开始，按照发展历程逐步深入了解项目的各个组成部分。*
