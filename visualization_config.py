#!/usr/bin/env python3
"""
统一的可视化配置文件
定义项目中所有可视化图表的颜色、范围、样式等统一标准

作者: AI Assistant
日期: 2025-07-02
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import xarray as xr

# ==================== 颜色配置 ====================

# SST场可视化配色方案
SST_COLORMAP = 'plasma'  # 统一使用plasma色图
SST_VMIN = 20.0  # SST最小值 (°C)
SST_VMAX = 32.0  # SST最大值 (°C)

# 误差场可视化配色方案
ERROR_COLORMAP = 'RdBu_r'  # 红蓝发散色图，红色表示正误差，蓝色表示负误差
ERROR_VMIN = -2.0  # 误差最小值 (°C)
ERROR_VMAX = 2.0   # 误差最大值 (°C)

# RMSE空间分布配色方案
RMSE_COLORMAP = 'YlOrRd'  # 黄橙红色图，适合表示强度
RMSE_VMIN = 0.0    # RMSE最小值
RMSE_VMAX = 1.0    # RMSE最大值 (°C)

# 时间序列图配色方案
TIMESERIES_COLORS = {
    'true': '#1f77b4',      # 蓝色 - 真实值
    'predicted': '#ff7f0e', # 橙色 - 预测值
    'error': '#d62728',     # 红色 - 误差
    'rmse': '#2ca02c'       # 绿色 - RMSE
}

# 模型对比配色方案
MODEL_COLORS = {
    'sst_only': '#808080',           # 灰色 - 纯SST基线
    'sst_pcs': '#1f77b4',           # 蓝色 - SST+PCs
    'sst_era5': '#ff7f0e',          # 橙色 - SST+ERA5
    'sst_pcs_era5': '#2ca02c',      # 绿色 - SST+PCs+ERA5
    'improved_selective': '#d62728', # 红色 - 改进-选择性PCs
    'improved_weighted': '#9467bd',  # 紫色 - 改进-加权融合
    'improved_hierarchical': '#8c564b' # 棕色 - 改进-分层融合
}

# ==================== 图表样式配置 ====================

# 图表尺寸
FIGURE_SIZES = {
    'single_map': (12, 8),      # 单个地图
    'triple_comparison': (18, 6), # 三联图对比
    'time_series': (12, 6),     # 时间序列
    'performance_bar': (15, 10), # 性能对比柱状图
    'scatter_plot': (10, 8)     # 散点图
}

# 字体大小
FONT_SIZES = {
    'title': 14,
    'label': 12,
    'tick': 10,
    'legend': 11,
    'annotation': 9
}

# DPI设置
DPI = 300

# ==================== 日期处理函数 ====================

def get_sample_dates(start_date='2020-01-01', n_samples=498, seq_len=14):
    """
    获取样本对应的日期
    
    参数:
    - start_date: 测试集开始日期
    - n_samples: 样本数量
    - seq_len: 序列长度
    
    返回:
    - 样本日期列表
    """
    base_date = pd.to_datetime(start_date)
    sample_dates = []
    
    for i in range(n_samples):
        # 每个样本对应的预测日期 = 基准日期 + seq_len + i
        sample_date = base_date + timedelta(days=seq_len + i)
        sample_dates.append(sample_date)
    
    return sample_dates

def get_test_start_date(sst_file='SST-V2.nc', train_ratio=0.8, val_ratio=0.1):
    """
    根据数据集划分获取测试集开始日期
    
    参数:
    - sst_file: SST数据文件路径
    - train_ratio: 训练集比例
    - val_ratio: 验证集比例
    
    返回:
    - 测试集开始日期
    """
    try:
        sst_data = xr.open_dataset(sst_file)
        total_time = len(sst_data.time)
        test_start_idx = int(total_time * (train_ratio + val_ratio))
        test_start_date = pd.to_datetime(sst_data.time.values[test_start_idx])
        sst_data.close()
        return test_start_date
    except:
        # 如果无法读取文件，使用默认日期
        return pd.to_datetime('2020-01-01')

def format_date_for_title(date_obj):
    """
    格式化日期用于图表标题
    
    参数:
    - date_obj: datetime对象或字符串
    
    返回:
    - 格式化的日期字符串
    """
    if isinstance(date_obj, str):
        date_obj = pd.to_datetime(date_obj)
    
    return date_obj.strftime('%Y-%m-%d')

# ==================== 颜色范围自适应函数 ====================

def get_adaptive_sst_range(sst_data, percentile_range=(2, 98)):
    """
    根据数据自适应确定SST颜色范围
    
    参数:
    - sst_data: SST数据数组
    - percentile_range: 百分位数范围
    
    返回:
    - (vmin, vmax) 元组
    """
    vmin = np.percentile(sst_data, percentile_range[0])
    vmax = np.percentile(sst_data, percentile_range[1])
    
    # 确保范围合理
    vmin = max(vmin, SST_VMIN)
    vmax = min(vmax, SST_VMAX)
    
    return vmin, vmax

def get_adaptive_error_range(error_data, percentile_range=(2, 98)):
    """
    根据数据自适应确定误差颜色范围
    
    参数:
    - error_data: 误差数据数组
    - percentile_range: 百分位数范围
    
    返回:
    - (vmin, vmax) 元组
    """
    abs_max_error = max(
        abs(np.percentile(error_data, percentile_range[0])), 
        abs(np.percentile(error_data, percentile_range[1]))
    )
    
    # 限制在合理范围内
    abs_max_error = min(abs_max_error, abs(ERROR_VMAX))
    
    return -abs_max_error, abs_max_error

def get_adaptive_rmse_range(rmse_data, percentile_range=(0, 98)):
    """
    根据数据自适应确定RMSE颜色范围
    
    参数:
    - rmse_data: RMSE数据数组
    - percentile_range: 百分位数范围
    
    返回:
    - (vmin, vmax) 元组
    """
    vmin = np.percentile(rmse_data, percentile_range[0])
    vmax = np.percentile(rmse_data, percentile_range[1])
    
    # 确保范围合理
    vmin = max(vmin, RMSE_VMIN)
    vmax = min(vmax, RMSE_VMAX)
    
    return vmin, vmax

# ==================== 标准化绘图函数 ====================

def setup_map_axes(ax, extent=None):
    """
    设置地图坐标轴的标准样式
    
    参数:
    - ax: matplotlib轴对象
    - extent: 地图范围 [lon_min, lon_max, lat_min, lat_max]
    """
    ax.coastlines(resolution='50m')
    ax.gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    
    if extent:
        ax.set_extent(extent)

def add_standard_colorbar(im, ax, label, extend='both'):
    """
    添加标准样式的颜色条
    
    参数:
    - im: 图像对象
    - ax: 坐标轴对象
    - label: 颜色条标签
    - extend: 颜色条扩展方式
    
    返回:
    - 颜色条对象
    """
    cbar = plt.colorbar(im, ax=ax, pad=0.01, extend=extend)
    cbar.set_label(label, fontsize=FONT_SIZES['label'])
    return cbar

# ==================== 样本索引到日期的映射 ====================

def get_sample_date_mapping(n_samples=498, seq_len=14):
    """
    获取样本索引到日期的映射
    
    参数:
    - n_samples: 样本总数
    - seq_len: 序列长度
    
    返回:
    - 字典，键为样本索引，值为日期字符串
    """
    test_start_date = get_test_start_date()
    sample_dates = get_sample_dates(test_start_date.strftime('%Y-%m-%d'), n_samples, seq_len)
    
    date_mapping = {}
    for i, date in enumerate(sample_dates):
        date_mapping[i] = format_date_for_title(date)
    
    return date_mapping

# ==================== 导出配置 ====================

# 将主要配置导出为字典，方便其他脚本导入
VISUALIZATION_CONFIG = {
    'sst': {
        'colormap': SST_COLORMAP,
        'vmin': SST_VMIN,
        'vmax': SST_VMAX
    },
    'error': {
        'colormap': ERROR_COLORMAP,
        'vmin': ERROR_VMIN,
        'vmax': ERROR_VMAX
    },
    'rmse': {
        'colormap': RMSE_COLORMAP,
        'vmin': RMSE_VMIN,
        'vmax': RMSE_VMAX
    },
    'colors': {
        'timeseries': TIMESERIES_COLORS,
        'models': MODEL_COLORS
    },
    'figure_sizes': FIGURE_SIZES,
    'font_sizes': FONT_SIZES,
    'dpi': DPI
}

if __name__ == "__main__":
    # 测试配置
    print("🎨 可视化配置测试")
    print(f"SST颜色范围: {SST_VMIN}°C - {SST_VMAX}°C")
    print(f"误差颜色范围: {ERROR_VMIN}°C - {ERROR_VMAX}°C")
    print(f"RMSE颜色范围: {RMSE_VMIN}°C - {RMSE_VMAX}°C")
    
    # 测试日期映射
    date_mapping = get_sample_date_mapping(5, 14)
    print(f"样本日期映射示例: {date_mapping}")
