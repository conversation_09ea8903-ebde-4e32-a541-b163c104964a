import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # Set font that supports Chinese
rcParams['axes.unicode_minus'] = False    # Fix negative sign display
import os
import time
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from sklearn.metrics import mean_squared_error, mean_absolute_error
import warnings

# 忽略cartopy下载警告
warnings.filterwarnings('ignore', category=UserWarning, module='cartopy')

print("开始使用EOF-ConvLSTM预测结果重构SST场...")

# 创建结果文件夹
os.makedirs('eof_convlstm_reconstruction', exist_ok=True)
os.makedirs('eof_convlstm_figures', exist_ok=True)

# 选择模型类型
model_type = "basic"  # 可选: "basic" 或 "spatial"

# 加载EOF-ConvLSTM预测的PCs和真实的PCs
print("加载EOF-ConvLSTM预测的PCs和真实的PCs...")
predicted_pcs = np.load(f'eof_convlstm_results/test_predictions_{model_type}.npy')  # 形状: (samples, pred_len, k)
true_pcs = np.load(f'eof_convlstm_results/test_targets_{model_type}.npy')           # 形状: (samples, pred_len, k)

# 加载EOF和均值数据
print("加载EOF和均值数据...")
EOFs_k = np.load('results/EOFs_k.npy')                # EOF模态，形状: (空间点数*变量数, k)
X_mean = np.load('results/X_mean.npy')                # 数据均值，形状: (空间点数*变量数, 1)
sst_mean = np.load('results/sst_mean.npy')            # SST均值，形状: (n_lat, n_lon)
sst_std = np.load('results/sst_std.npy')              # SST标准差，形状: (n_lat, n_lon)

# 加载测试集时间信息
print("加载测试集时间信息...")
original_sst_data = xr.open_dataset('SST-V2.nc')
n_lat = len(original_sst_data.latitude)
n_lon = len(original_sst_data.longitude)

# 确定测试集的时间范围
total_time = len(original_sst_data.time)
test_start_idx = int(total_time * 0.9)
test_times = original_sst_data.time.values[test_start_idx:]

# 检查数据形状
print(f"预测的PCs形状: {predicted_pcs.shape}")
print(f"真实的PCs形状: {true_pcs.shape}")
print(f"EOFs形状: {EOFs_k.shape}")
print(f"数据均值形状: {X_mean.shape}")
print(f"测试集时间点数: {len(test_times)}")
print(f"纬度点数: {n_lat}, 经度点数: {n_lon}")

# 定义重构函数：从PCs重构回物理空间
def reconstruct_from_pcs(pcs, eofs, x_mean):
    """从PCs重构回物理空间"""
    # pcs形状: (samples, pred_len, k)
    # 需要重组为: (k, samples*pred_len)以便进行矩阵乘法
    samples, pred_len, k = pcs.shape
    pcs_reshaped = pcs.reshape(samples * pred_len, k).T  # 变为(k, samples*pred_len)
    
    # 乘以EOFs矩阵重构异常场
    # eofs形状: (空间点数*变量数, k)
    # 矩阵乘法后形状: (空间点数*变量数, samples*pred_len)
    reconstructed_anomalies = np.dot(eofs, pcs_reshaped)
    
    # 加上均值得到完整场
    # x_mean形状: (空间点数*变量数, 1)
    reconstructed_fields = reconstructed_anomalies + x_mean
    
    # 重组回原始形状: (samples, pred_len, 2, n_lat, n_lon)
    # 其中2表示变量数（SST和t2m）
    reconstructed_fields = reconstructed_fields.T.reshape(samples, pred_len, 2, n_lat, n_lon)
    
    return reconstructed_fields

# 从PCs重构回物理空间
print("从PCs重构回物理空间...")
reconstructed_pred = reconstruct_from_pcs(predicted_pcs, EOFs_k, X_mean)
reconstructed_true = reconstruct_from_pcs(true_pcs, EOFs_k, X_mean)

# 提取SST部分（第一个变量）
print("提取SST部分...")
sst_pred = reconstructed_pred[:, 0, 0, :, :]  # 形状: (samples, n_lat, n_lon)
sst_true = reconstructed_true[:, 0, 0, :, :]  # 形状: (samples, n_lat, n_lon)

# 反标准化SST（乘以标准差加上均值）
print("反标准化SST为实际温度值...")
sst_pred_unstd = sst_pred * sst_std + sst_mean
sst_true_unstd = sst_true * sst_std + sst_mean

# 将开尔文温度转换为摄氏度
print("将开尔文温度转换为摄氏度...")
sst_pred_celsius = sst_pred_unstd - 273.15
sst_true_celsius = sst_true_unstd - 273.15

# 计算误差
print("计算预测误差...")
sst_error = sst_pred_celsius - sst_true_celsius  # 形状: (samples, n_lat, n_lon)

# 计算整体RMSE和MAE（摄氏度）
rmse = np.sqrt(np.mean(sst_error ** 2))
mae = np.mean(np.abs(sst_error))
print(f"EOF-ConvLSTM ({model_type}) SST场预测的整体RMSE: {rmse:.4f}°C")
print(f"EOF-ConvLSTM ({model_type}) SST场预测的整体MAE: {mae:.4f}°C")

# 保存重构结果
print("保存重构结果...")
np.save(f'eof_convlstm_reconstruction/sst_pred_celsius_{model_type}.npy', sst_pred_celsius)
np.save(f'eof_convlstm_reconstruction/sst_true_celsius_{model_type}.npy', sst_true_celsius)
np.save(f'eof_convlstm_reconstruction/sst_error_celsius_{model_type}.npy', sst_error)

# 保存误差统计
error_stats = {
    'rmse': rmse,
    'mae': mae,
    'model_type': model_type
}
np.save(f'eof_convlstm_reconstruction/error_stats_{model_type}.npy', error_stats)

# 创建包含预测结果的NetCDF文件
print("创建包含预测结果的NetCDF文件...")
if len(test_times) > sst_pred_celsius.shape[0]:
    test_times = test_times[:sst_pred_celsius.shape[0]]

# 创建xarray数据集
ds_pred = xr.Dataset(
    data_vars={
        'sst_pred': (['time', 'latitude', 'longitude'], sst_pred_celsius),
        'sst_true': (['time', 'latitude', 'longitude'], sst_true_celsius),
        'sst_error': (['time', 'latitude', 'longitude'], sst_error)
    },
    coords={
        'time': test_times,
        'latitude': original_sst_data.latitude.values,
        'longitude': original_sst_data.longitude.values
    },
    attrs={
        'description': f'EOF-ConvLSTM ({model_type}) SST预测结果和误差',
        'units': '摄氏度',
        'created': time.ctime(),
        'model_type': model_type
    }
)

# 保存为NetCDF文件
ds_pred.to_netcdf(f'eof_convlstm_reconstruction/sst_prediction_results_{model_type}.nc')

# 可视化预测结果
print("可视化预测结果...")

# 设置地图属性
map_proj = ccrs.PlateCarree()

# 预先下载自然地球数据以避免下载警告
print("准备地图特征数据...")
coastlines = cfeature.NaturalEarthFeature('physical', 'coastline', '50m')
land = cfeature.NaturalEarthFeature('physical', 'land', '50m', 
                                   edgecolor='black', facecolor='lightgray', alpha=0.5)

# 定义并排可视化函数
def plot_sst_comparison(true_data, pred_data, error_data, day_str, filename):
    """绘制并排的真实SST、预测SST和误差场"""
    fig, axes = plt.subplots(1, 3, figsize=(24, 8), subplot_kw={'projection': map_proj})
    
    # 通过计算百分位数确定合适的颜色范围
    vmin_sst = np.percentile(true_data, 2)
    vmax_sst = np.percentile(true_data, 98)
    
    # 误差范围
    abs_max_error = max(abs(np.percentile(error_data, 2)), abs(np.percentile(error_data, 98)))
    vmin_err, vmax_err = -abs_max_error, abs_max_error
    
    # 1. 真实SST场
    axes[0].set_title(f'真实SST场 ({day_str})', fontsize=14)
    axes[0].coastlines(resolution='50m')
    axes[0].add_feature(land)
    axes[0].gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    im1 = axes[0].pcolormesh(
        original_sst_data.longitude, 
        original_sst_data.latitude, 
        true_data, 
        cmap='plasma',
        vmin=vmin_sst, 
        vmax=vmax_sst,
        transform=ccrs.PlateCarree()
    )
    cbar1 = fig.colorbar(im1, ax=axes[0], pad=0.01, extend='both')
    cbar1.set_label('温度 (°C)', fontsize=12)
    
    # 2. 预测SST场
    axes[1].set_title(f'EOF-ConvLSTM ({model_type}) 预测SST场 ({day_str})', fontsize=14)
    axes[1].coastlines(resolution='50m')
    axes[1].add_feature(land)
    axes[1].gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    im2 = axes[1].pcolormesh(
        original_sst_data.longitude, 
        original_sst_data.latitude, 
        pred_data, 
        cmap='plasma',
        vmin=vmin_sst, 
        vmax=vmax_sst,
        transform=ccrs.PlateCarree()
    )
    cbar2 = fig.colorbar(im2, ax=axes[1], pad=0.01, extend='both')
    cbar2.set_label('温度 (°C)', fontsize=12)
    
    # 3. 误差场
    axes[2].set_title(f'预测误差 ({day_str})', fontsize=14)
    axes[2].coastlines(resolution='50m')
    axes[2].add_feature(land)
    axes[2].gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    im3 = axes[2].pcolormesh(
        original_sst_data.longitude, 
        original_sst_data.latitude, 
        error_data, 
        cmap='RdBu_r',
        vmin=vmin_err, 
        vmax=vmax_err,
        transform=ccrs.PlateCarree()
    )
    cbar3 = fig.colorbar(im3, ax=axes[2], pad=0.01, extend='both')
    cbar3.set_label('温差 (°C)', fontsize=12)
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()

# 可视化示例时间点
sample_days = [0, len(sst_pred_celsius)//4, len(sst_pred_celsius)//2, 3*len(sst_pred_celsius)//4, -1]

for i, day_idx in enumerate(sample_days):
    day_idx = day_idx if day_idx >= 0 else len(sst_pred_celsius) + day_idx
    
    if day_idx < len(test_times):
        day_str = str(test_times[day_idx]).split('T')[0]
        
        print(f"生成第{i+1}/5个样本的可视化图像: {day_str}...")
        plot_sst_comparison(
            sst_true_celsius[day_idx],
            sst_pred_celsius[day_idx],
            sst_error[day_idx],
            day_str,
            f'eof_convlstm_figures/sst_comparison_day{day_idx}_{model_type}.png'
        )

# 计算时间平均场
print("计算时间平均场和误差统计...")
mean_true_sst = np.mean(sst_true_celsius, axis=0)
mean_pred_sst = np.mean(sst_pred_celsius, axis=0)
mean_error = np.mean(sst_error, axis=0)
rmse_map = np.sqrt(np.mean(sst_error**2, axis=0))

# 绘制时间平均对比图
print("生成时间平均场的可视化...")
plot_sst_comparison(
    mean_true_sst,
    mean_pred_sst,
    mean_error,
    '时间平均',
    f'eof_convlstm_figures/time_averaged_comparison_{model_type}.png'
)

# 绘制RMSE分布图
plt.figure(figsize=(12, 8))
ax = plt.axes(projection=map_proj)
ax.coastlines(resolution='50m')
ax.add_feature(land)
ax.gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')

vmax_rmse = np.percentile(rmse_map, 98)
im = ax.pcolormesh(
    original_sst_data.longitude,
    original_sst_data.latitude,
    rmse_map,
    cmap='YlOrRd',
    vmin=0,
    vmax=vmax_rmse,
    transform=ccrs.PlateCarree()
)
cbar = plt.colorbar(im, ax=ax, pad=0.01, extend='max')
cbar.set_label('RMSE (°C)', fontsize=12)
plt.title(f'EOF-ConvLSTM ({model_type}) 时间平均RMSE分布图', fontsize=14)
plt.savefig(f'eof_convlstm_figures/rmse_map_{model_type}.png', dpi=300, bbox_inches='tight')
plt.close()

# 绘制误差时间序列
plt.figure(figsize=(12, 6))
time_steps = range(len(sst_error))
mean_abs_error = np.mean(np.abs(sst_error), axis=(1, 2))
rmse_time = np.sqrt(np.mean(sst_error**2, axis=(1, 2)))

plt.plot(time_steps, mean_abs_error, label='平均绝对误差', color='blue')
plt.plot(time_steps, rmse_time, label='RMSE', color='red')
plt.xlabel('时间步', fontsize=12)
plt.ylabel('误差 (°C)', fontsize=12)
plt.title(f'EOF-ConvLSTM ({model_type}) SST预测误差随时间变化', fontsize=14)
plt.legend(fontsize=12)
plt.grid(True)
plt.tight_layout()
plt.savefig(f'eof_convlstm_figures/error_time_series_{model_type}.png', dpi=300, bbox_inches='tight')
plt.close()

print(f"EOF-ConvLSTM ({model_type}) SST场重构和可视化完成！")

# 关闭文件
plt.close('all')
original_sst_data.close()
