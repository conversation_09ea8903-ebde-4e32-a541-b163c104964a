#!/usr/bin/env python3
"""
EOF加权空间映射ConvLSTM结果可视化脚本
可视化策略2的预测结果、误差分布和EOF特征分析

作者: AI Assistant
日期: 2025-07-07
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False
import xarray as xr
import os
from datetime import datetime, timedelta
from tqdm import tqdm

# 导入统一的可视化配置
import sys
import os

# 获取项目根目录并添加到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)

try:
    from visualization_config import (
        SST_COLORMAP, SST_VMIN, SST_VMAX,
        ERROR_COLORMAP, ERROR_VMIN, ERROR_VMAX,
        RMSE_COLORMAP, RMSE_VMIN, RMSE_VMAX,
        TIMESERIES_COLORS, FIGURE_SIZES, FONT_SIZES, DPI,
        get_sample_date_mapping, format_date_for_title
    )
    print("✅ 成功导入可视化配置")
except ImportError as e:
    print(f"❌ 无法导入可视化配置: {e}")
    print("使用默认配置...")

    # 定义默认配置
    SST_COLORMAP = 'plasma'
    SST_VMIN, SST_VMAX = 20.0, 32.0
    ERROR_COLORMAP = 'RdBu_r'
    ERROR_VMIN, ERROR_VMAX = -2.0, 2.0
    RMSE_COLORMAP = 'YlOrRd'
    RMSE_VMIN, RMSE_VMAX = 0.0, 1.0

    TIMESERIES_COLORS = {
        'true': '#1f77b4',
        'predicted': '#ff7f0e',
        'error': '#d62728',
        'rmse': '#2ca02c'
    }

    FIGURE_SIZES = {
        'triple_comparison': (18, 6),
        'single_map': (12, 8),
        'time_series': (12, 6)
    }

    FONT_SIZES = {
        'title': 14,
        'label': 12,
        'legend': 11,
        'tick': 10
    }

    DPI = 300

    def get_sample_date_mapping(n_samples, seq_len=14):
        """简单的日期映射函数"""
        from datetime import datetime, timedelta
        base_date = datetime(2020, 1, 1)
        date_mapping = {}
        for i in range(n_samples):
            sample_date = base_date + timedelta(days=seq_len + i)
            date_mapping[i] = sample_date.strftime('%Y-%m-%d')
        return date_mapping

    def format_date_for_title(date_str):
        """格式化日期"""
        return str(date_str)

print("🎨 开始EOF加权空间映射ConvLSTM结果可视化...")

# 创建结果文件夹
os.makedirs('eof_weighted_visualization', exist_ok=True)

# 加载标准化参数
print("📊 加载标准化参数...")
sst_mean_path = os.path.join(project_root, 'results', 'sst_mean.npy')
sst_std_path = os.path.join(project_root, 'results', 'sst_std.npy')

sst_mean = np.load(sst_mean_path)
sst_std = np.load(sst_std_path)

# 如果是数组，取第一个元素
if isinstance(sst_mean, np.ndarray):
    sst_mean = sst_mean.item() if sst_mean.size == 1 else sst_mean.mean()
if isinstance(sst_std, np.ndarray):
    sst_std = sst_std.item() if sst_std.size == 1 else sst_std.mean()

print(f"SST标准化参数: mean={sst_mean:.3f}, std={sst_std:.3f}")

def denormalize_sst(sst_norm):
    """反标准化SST数据"""
    return sst_norm * sst_std + sst_mean

def kelvin_to_celsius(temp_k):
    """开尔文转摄氏度"""
    return temp_k - 273.15

def visualize_eof_weighted_results(model_name):
    """可视化EOF加权模型结果"""
    print(f"\n🔍 可视化 {model_name} EOF加权模型结果...")
    
    # 加载预测结果
    try:
        predictions = np.load(f'eof_weighted_results/test_predictions_{model_name}.npy')
        targets = np.load(f'eof_weighted_results/test_targets_{model_name}.npy')
    except FileNotFoundError:
        print(f"❌ 未找到 {model_name} 的预测结果文件")
        return
    
    print(f"预测结果形状: {predictions.shape}")
    print(f"目标结果形状: {targets.shape}")
    
    # 反标准化为实际温度值
    predictions_denorm = denormalize_sst(predictions)
    targets_denorm = denormalize_sst(targets)
    
    # 转换为摄氏度
    predictions_celsius = kelvin_to_celsius(predictions_denorm)
    targets_celsius = kelvin_to_celsius(targets_denorm)
    
    # 计算误差
    error_celsius = predictions_celsius - targets_celsius
    
    print(f"SST预测范围: {predictions_celsius.min():.2f}°C - {predictions_celsius.max():.2f}°C")
    print(f"SST真实范围: {targets_celsius.min():.2f}°C - {targets_celsius.max():.2f}°C")
    print(f"误差范围: {error_celsius.min():.2f}°C - {error_celsius.max():.2f}°C")
    
    # 计算统计指标
    rmse = np.sqrt(np.mean(error_celsius**2))
    mae = np.mean(np.abs(error_celsius))
    
    print(f"RMSE: {rmse:.4f}°C")
    print(f"MAE: {mae:.4f}°C")
    
    # 保存摄氏度结果
    np.save(f'eof_weighted_visualization/sst_pred_celsius_{model_name}.npy', predictions_celsius)
    np.save(f'eof_weighted_visualization/sst_true_celsius_{model_name}.npy', targets_celsius)
    np.save(f'eof_weighted_visualization/sst_error_celsius_{model_name}.npy', error_celsius)
    
    # 读取原始SST数据以获取坐标信息
    sst_data_path = os.path.join(project_root, 'SST-V2.nc')
    sst_data = xr.open_dataset(sst_data_path)
    
    # 获取样本日期映射
    date_mapping = get_sample_date_mapping(len(predictions), seq_len=14)
    
    # 可视化几个时间点的SST对比
    sample_indices = [0, len(predictions)//4, len(predictions)//2, 
                     3*len(predictions)//4, len(predictions)-1]
    
    for i, sample_idx in enumerate(sample_indices):
        if sample_idx >= len(predictions):
            continue
        
        # 获取样本日期
        sample_date = date_mapping.get(sample_idx, f'样本{sample_idx}')
            
        fig, axes = plt.subplots(1, 3, figsize=FIGURE_SIZES['triple_comparison'])
        
        # 真实SST
        im1 = axes[0].imshow(targets_celsius[sample_idx, 0], cmap=SST_COLORMAP, 
                            vmin=SST_VMIN, vmax=SST_VMAX,
                            extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                   sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[0].set_title(f'真实SST (°C)\n{sample_date}', fontsize=FONT_SIZES['title'])
        axes[0].set_xlabel('经度', fontsize=FONT_SIZES['label'])
        axes[0].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
        cbar1 = plt.colorbar(im1, ax=axes[0])
        cbar1.set_label('SST (°C)', fontsize=FONT_SIZES['label'])
        
        # 预测SST
        im2 = axes[1].imshow(predictions_celsius[sample_idx, 0], cmap=SST_COLORMAP, 
                            vmin=SST_VMIN, vmax=SST_VMAX,
                            extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                   sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[1].set_title(f'预测SST (°C)\n{sample_date} [EOF加权]', fontsize=FONT_SIZES['title'])
        axes[1].set_xlabel('经度', fontsize=FONT_SIZES['label'])
        axes[1].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
        cbar2 = plt.colorbar(im2, ax=axes[1])
        cbar2.set_label('SST (°C)', fontsize=FONT_SIZES['label'])
        
        # 误差
        im3 = axes[2].imshow(error_celsius[sample_idx, 0], cmap=ERROR_COLORMAP, 
                            vmin=ERROR_VMIN, vmax=ERROR_VMAX,
                            extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                   sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[2].set_title(f'预测误差 (°C)\n{sample_date} [EOF加权]', fontsize=FONT_SIZES['title'])
        axes[2].set_xlabel('经度', fontsize=FONT_SIZES['label'])
        axes[2].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
        cbar3 = plt.colorbar(im3, ax=axes[2])
        cbar3.set_label('误差 (°C)', fontsize=FONT_SIZES['label'])
        
        plt.tight_layout()
        plt.savefig(f'eof_weighted_visualization/sst_comparison_sample{sample_idx}_{model_name}.png', 
                   dpi=DPI, bbox_inches='tight')
        plt.close()
    
    # 绘制RMSE空间分布
    rmse_spatial = np.sqrt(np.mean(error_celsius**2, axis=0))
    
    plt.figure(figsize=FIGURE_SIZES['single_map'])
    im = plt.imshow(rmse_spatial[0], cmap=RMSE_COLORMAP,
                   vmin=RMSE_VMIN, vmax=RMSE_VMAX,
                   extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                          sst_data.latitude.values.min(), sst_data.latitude.values.max()])
    plt.title(f'{model_name} EOF加权模型 - RMSE空间分布 (°C)', fontsize=FONT_SIZES['title'])
    plt.xlabel('经度', fontsize=FONT_SIZES['label'])
    plt.ylabel('纬度', fontsize=FONT_SIZES['label'])
    cbar = plt.colorbar(im)
    cbar.set_label('RMSE (°C)', fontsize=FONT_SIZES['label'])
    plt.savefig(f'eof_weighted_visualization/rmse_map_{model_name}.png', 
               dpi=DPI, bbox_inches='tight')
    plt.close()
    
    # 绘制误差时间序列
    spatial_mean_error = np.mean(error_celsius, axis=(1, 2, 3))
    spatial_rmse_time = np.sqrt(np.mean(error_celsius**2, axis=(1, 2, 3)))
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=FIGURE_SIZES['time_series'])
    
    # 平均误差时间序列
    ax1.plot(spatial_mean_error, color=TIMESERIES_COLORS['error'], alpha=0.7, linewidth=1.5)
    ax1.set_title(f'{model_name} EOF加权模型 - 空间平均误差时间序列', fontsize=FONT_SIZES['title'])
    ax1.set_ylabel('平均误差 (°C)', fontsize=FONT_SIZES['label'])
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax1.tick_params(labelsize=FONT_SIZES['tick'])
    
    # RMSE时间序列
    ax2.plot(spatial_rmse_time, color=TIMESERIES_COLORS['rmse'], alpha=0.7, linewidth=1.5)
    ax2.set_title(f'{model_name} EOF加权模型 - RMSE时间序列', fontsize=FONT_SIZES['title'])
    ax2.set_xlabel('时间步', fontsize=FONT_SIZES['label'])
    ax2.set_ylabel('RMSE (°C)', fontsize=FONT_SIZES['label'])
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(labelsize=FONT_SIZES['tick'])
    
    plt.tight_layout()
    plt.savefig(f'eof_weighted_visualization/error_time_series_{model_name}.png', 
               dpi=DPI, bbox_inches='tight')
    plt.close()
    
    # 绘制空间平均SST时间序列对比
    spatial_mean_pred = np.mean(predictions_celsius, axis=(1, 2, 3))
    spatial_mean_true = np.mean(targets_celsius, axis=(1, 2, 3))
    
    plt.figure(figsize=FIGURE_SIZES['time_series'])
    plt.plot(spatial_mean_true, label='真实SST', color=TIMESERIES_COLORS['true'], 
             alpha=0.8, linewidth=1.5)
    plt.plot(spatial_mean_pred, label='预测SST (EOF加权)', color=TIMESERIES_COLORS['predicted'], 
             alpha=0.8, linewidth=1.5)
    plt.title(f'{model_name} EOF加权模型 - 空间平均SST时间序列对比', fontsize=FONT_SIZES['title'])
    plt.xlabel('时间步', fontsize=FONT_SIZES['label'])
    plt.ylabel('SST (°C)', fontsize=FONT_SIZES['label'])
    plt.legend(fontsize=FONT_SIZES['legend'])
    plt.grid(True, alpha=0.3)
    plt.tick_params(labelsize=FONT_SIZES['tick'])
    plt.savefig(f'eof_weighted_visualization/spatial_mean_timeseries_{model_name}.png', 
               dpi=DPI, bbox_inches='tight')
    plt.close()
    
    sst_data.close()
    print(f"✅ {model_name} EOF加权模型可视化完成！")

def visualize_eof_features():
    """可视化EOF加权特征"""
    print("\n🔍 可视化EOF加权特征...")
    
    # 加载EOF空间模态
    try:
        eof_sst_spatial = np.load('eof_weighted_data/EOFs_sst_spatial.npy')
        eof_weighted_features = np.load('eof_weighted_data/test_eof_weighted_features.npy')
    except FileNotFoundError:
        print("❌ 未找到EOF特征文件")
        return
    
    print(f"SST EOF空间模态形状: {eof_sst_spatial.shape}")
    print(f"EOF加权特征形状: {eof_weighted_features.shape}")
    
    # 读取坐标信息
    sst_data_path = os.path.join(project_root, 'SST-V2.nc')
    sst_data = xr.open_dataset(sst_data_path)
    
    # 可视化前几个EOF模态
    n_modes_to_show = min(6, eof_sst_spatial.shape[2])
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i in range(n_modes_to_show):
        im = axes[i].imshow(eof_sst_spatial[:, :, i], cmap='RdBu_r', 
                           vmin=-0.1, vmax=0.1,
                           extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                  sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[i].set_title(f'EOF模态 {i+1}', fontsize=FONT_SIZES['title'])
        axes[i].set_xlabel('经度', fontsize=FONT_SIZES['label'])
        axes[i].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
        plt.colorbar(im, ax=axes[i])
    
    plt.tight_layout()
    plt.savefig('eof_weighted_visualization/eof_spatial_modes.png', 
               dpi=DPI, bbox_inches='tight')
    plt.close()
    
    # 可视化某个时间点的EOF加权特征
    time_idx = 100  # 选择一个时间点
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i in range(n_modes_to_show):
        im = axes[i].imshow(eof_weighted_features[time_idx, i], cmap='RdBu_r',
                           extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                  sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[i].set_title(f'EOF加权特征 {i+1}\n时间步 {time_idx}', fontsize=FONT_SIZES['title'])
        axes[i].set_xlabel('经度', fontsize=FONT_SIZES['label'])
        axes[i].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
        plt.colorbar(im, ax=axes[i])
    
    plt.tight_layout()
    plt.savefig('eof_weighted_visualization/eof_weighted_features_example.png', 
               dpi=DPI, bbox_inches='tight')
    plt.close()
    
    sst_data.close()
    print("✅ EOF特征可视化完成！")

# 主可视化流程
if __name__ == "__main__":
    # 可视化EOF特征
    visualize_eof_features()
    
    # 可视化模型结果
    model_types = ["eof_weighted_basic"]
    
    for model_name in model_types:
        visualize_eof_weighted_results(model_name)
    
    print("\n🎉 EOF加权空间映射ConvLSTM结果可视化完成！")
    print("📁 结果保存在 eof_weighted_visualization/ 文件夹中")
    
    # 生成总结报告
    print("\n📋 生成实验总结...")
    
    summary_text = f"""
# EOF加权空间映射ConvLSTM实验总结

## 实验设计
- **策略**: 策略2 - EOF加权空间映射
- **核心思想**: 使用EOF模态的空间结构对PCs进行加权映射
- **输入特征**: SST + ERA5 + EOF加权空间场

## 模型架构
- **输入通道**: 1(SST) + 4(ERA5) + 10(EOF加权特征) = 15通道
- **网络结构**: ConvLSTM + 注意力机制 + 残差连接
- **序列长度**: 14天预测1天

## 技术创新
1. **EOF空间映射**: 将PCs时间系数映射为空间加权场
2. **多特征融合**: 结合SST、ERA5和EOF特征的优势
3. **物理约束**: 保持EOF模态的空间结构信息
4. **注意力机制**: 自适应融合不同类型的特征

## 预期优势
- **物理意义**: EOF加权场保持了海洋动力学的空间结构
- **特征丰富**: 结合了时空信息和物理模态信息
- **自适应性**: 通过注意力机制动态调整特征权重

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('eof_weighted_visualization/experiment_summary.md', 'w', encoding='utf-8') as f:
        f.write(summary_text)
    
    print("✅ 实验总结已保存到 eof_weighted_visualization/experiment_summary.md")
