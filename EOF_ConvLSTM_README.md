# EOF-ConvLSTM SST预测方案

## 概述

本方案实现了将EOF分解后的主成分(PCs)作为ConvLSTM模型输入来预测海表温度(SST)的创新方法。该方案结合了EOF降维的优势和ConvLSTM的时空建模能力，为地球物理场预测提供了新的思路。

## 核心思想

### 传统方法 vs 新方案

**传统Transformer方法:**
```
PCs时间序列 → Transformer → 预测PCs → EOF重构 → SST场
```

**EOF-ConvLSTM方法:**
```
PCs时间序列 → 空间化表示 → ConvLSTM → 预测PCs → EOF重构 → SST场
```

### 技术创新点

1. **PCs空间化**: 将1D的主成分时间系数转换为2D空间表示
2. **时空建模**: 利用ConvLSTM同时捕捉时间和空间依赖关系
3. **残差连接**: 保持时序信息的连续性
4. **双重架构**: 提供基础版和空间版两种实现

## 模型架构

### 1. 基础EOF-ConvLSTM模型 (EOFConvLSTMModel)

```python
输入: PCs [batch_size, seq_len, num_pcs]
  ↓
PC到空间映射: Linear(num_pcs → spatial_size × spatial_size × 16)
  ↓
ConvLSTM: 时空序列建模
  ↓
空间到PC映射: Conv2d + AdaptiveAvgPool2d + Linear
  ↓
残差连接: 与输入最后时间步相加
  ↓
输出: 预测PCs [batch_size, pred_len, num_pcs]
```

**特点:**
- 使用全连接层将PCs映射到固定大小的空间表示
- 适用于任意数量的主成分
- 计算效率高

### 2. 空间EOF-ConvLSTM模型 (SpatialEOFConvLSTMModel)

```python
输入: PCs [batch_size, seq_len, num_pcs]
  ↓
EOF空间映射: PCs × EOF_spatial_maps
  ↓
ConvLSTM: 在真实空间网格上建模
  ↓
空间投影: Conv2d层处理
  ↓
PC聚合: 空间平均或其他聚合方式
  ↓
输出: 预测PCs [batch_size, pred_len, num_pcs]
```

**特点:**
- 直接使用EOF空间模态进行映射
- 保持物理意义
- 在真实空间网格上进行建模

## 文件结构

```
├── eof_convlstm_model.py          # ConvLSTM模型定义
├── train_eof_convlstm.py          # 训练脚本
├── reconstruct_sst_convlstm.py    # SST重构脚本
├── compare_models.py              # 模型比较脚本
├── run_eof_convlstm_pipeline.py   # 完整流水线脚本
└── EOF_ConvLSTM_README.md         # 本文档
```

## 使用方法

### 前置条件

确保已完成EOF分解：
```bash
python preprocess_and_eof.py
```

### 方法1: 使用完整流水线

```bash
# 训练两种模型并进行比较
python run_eof_convlstm_pipeline.py --model-type both

# 只训练基础模型
python run_eof_convlstm_pipeline.py --model-type basic

# 只训练空间模型
python run_eof_convlstm_pipeline.py --model-type spatial

# 跳过训练，只进行重构和比较
python run_eof_convlstm_pipeline.py --skip-training
```

### 方法2: 分步执行

```bash
# 1. 训练模型
python train_eof_convlstm.py

# 2. 重构SST场
python reconstruct_sst_convlstm.py

# 3. 模型比较
python compare_models.py
```

## 超参数配置

### 模型超参数

```python
# 基础模型
EOFConvLSTMModel(
    num_pcs=10,           # 主成分数量
    seq_len=14,           # 输入序列长度
    pred_len=1,           # 预测长度
    hidden_dim=64,        # ConvLSTM隐藏维度
    kernel_size=(3, 3),   # 卷积核大小
    num_layers=2,         # ConvLSTM层数
    spatial_size=(8, 8),  # 空间表示大小
    dropout=0.1           # Dropout率
)
```

### 训练超参数

```python
batch_size = 32
learning_rate = 0.001
weight_decay = 1e-4
num_epochs = 80
patience = 15
clip_grad = 1.0
```

## 输出结果

### 训练结果
- `eof_convlstm_results/best_eof_convlstm_{model_type}.pth`: 最佳模型权重
- `eof_convlstm_results/test_predictions_{model_type}.npy`: PCs预测结果
- `eof_convlstm_figures/training_curves_{model_type}.png`: 训练曲线

### 重构结果
- `eof_convlstm_reconstruction/sst_pred_celsius_{model_type}.npy`: SST预测场
- `eof_convlstm_reconstruction/sst_prediction_results_{model_type}.nc`: NetCDF格式结果
- `eof_convlstm_figures/sst_comparison_*.png`: SST对比图

### 比较结果
- `model_comparison/performance_comparison.csv`: 性能比较表
- `model_comparison/pcs_error_comparison.png`: PCs误差比较
- `model_comparison/sst_error_comparison.png`: SST误差比较

## 技术优势

### 1. 降维优势
- 在低维PCs空间进行预测，计算效率高
- 保留主要的物理信息，去除噪声

### 2. 时空建模
- ConvLSTM同时捕捉时间和空间依赖关系
- 比纯时间序列模型更适合地球物理场预测

### 3. 物理意义
- 基于EOF分解，保持物理可解释性
- 空间版本直接在物理网格上建模

### 4. 灵活性
- 提供两种架构选择
- 可根据具体应用调整超参数

## 性能评估

模型性能通过以下指标评估：

1. **PCs预测精度**
   - MSE: 均方误差
   - MAE: 平均绝对误差

2. **SST重构精度**
   - RMSE: 均方根误差 (°C)
   - MAE: 平均绝对误差 (°C)

3. **空间分布误差**
   - RMSE空间分布图
   - 时间平均误差场

## 应用建议

### 1. 模型选择
- **基础模型**: 适用于快速原型和计算资源有限的情况
- **空间模型**: 适用于需要保持物理意义的应用

### 2. 超参数调优
- `spatial_size`: 影响基础模型的空间表示能力
- `hidden_dim`: 影响模型容量和计算复杂度
- `num_layers`: 影响模型深度和表达能力

### 3. 数据预处理
- 确保PCs数据标准化
- 考虑时间序列的平稳性

## 扩展方向

1. **多变量预测**: 同时预测SST和其他变量
2. **长期预测**: 扩展到多步预测
3. **注意力机制**: 在ConvLSTM中加入注意力
4. **集成学习**: 结合多个模型的预测结果

## 注意事项

1. 确保EOF分解结果的质量
2. 注意PCs的时间对齐
3. 合理设置空间表示的大小
4. 监控训练过程中的梯度变化

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库: [GitHub链接]
- 邮箱: [联系邮箱]

---

*本方案为EOF分解与ConvLSTM结合的创新实现，为地球物理场预测提供了新的技术路径。*
