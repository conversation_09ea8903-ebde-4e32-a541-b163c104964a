# SST+ERA5 ConvLSTM 消融实验设计

## 🔬 实验概述

本消融实验旨在**量化PCs特征对SST预测性能的贡献**，通过移除PCs输入来验证其在多源数据融合模型中的重要性。

## 🎯 实验目标

1. **量化PCs贡献**: 测量移除PCs特征后模型性能的下降程度
2. **验证特征重要性**: 确认PCs特征在SST预测中的必要性
3. **优化模型设计**: 为未来模型改进提供数据支持

## 📊 实验设计

### 对比组设置

| 模型组 | 输入特征 | 通道数 | 描述 |
|--------|----------|--------|------|
| **完整模型** | SST + PCs + ERA5 | 15 | 1(SST) + 10(PCs) + 4(ERA5) |
| **消融模型** | SST + ERA5 | 5 | 1(SST) + 4(ERA5)，移除PCs |
| **基线模型** | SST + PCs | 11 | 1(SST) + 10(PCs)，已有对比 |

### 控制变量

为确保实验的公平性，以下参数在所有模型中保持一致：

```python
# 模型超参数
seq_len = 14          # 输入序列长度
pred_len = 1          # 预测长度  
hidden_dim = 64       # ConvLSTM隐藏维度
num_layers = 2        # ConvLSTM层数
batch_size = 8        # 批次大小
learning_rate = 0.001 # 学习率
num_epochs = 80       # 最大训练轮数
patience = 15         # 早停耐心值
```

### 评估指标

- **RMSE (°C)**: 均方根误差
- **MAE (°C)**: 平均绝对误差
- **空间误差分布**: RMSE空间分布图
- **时间序列性能**: 误差随时间变化

## 🏗️ 模型架构对比

### 完整模型架构
```
输入: [SST, PCs_spatial, u10, v10, t2m, msl] 
     → [batch_size, seq_len, 15, lat, lon]
  ↓
通道注意力: 15个通道的权重学习
  ↓
ConvLSTM: 时空序列建模
  ↓
输出: 预测SST [batch_size, 1, lat, lon]
```

### 消融模型架构
```
输入: [SST, u10, v10, t2m, msl] 
     → [batch_size, seq_len, 5, lat, lon]
  ↓
通道注意力: 5个通道的权重学习
  ↓
ConvLSTM: 时空序列建模
  ↓
输出: 预测SST [batch_size, 1, lat, lon]
```

### 关键差异

1. **输入维度**: 15通道 vs 5通道
2. **特征信息**: 包含/不包含PCs全局模态信息
3. **参数量**: 消融模型参数更少
4. **计算复杂度**: 消融模型计算更快

## 📁 文件结构

```
├── eof_convlstm_model.py              # 新增消融模型类
│   ├── SSTWithERA5ConvLSTMModel       # 基础消融模型
│   └── MultiScaleSSTWithERA5ConvLSTMModel # 多尺度消融模型
├── train_sst_era5_convlstm.py         # 消融实验训练脚本
├── visualize_sst_era5_convlstm.py     # 消融实验可视化
├── run_ablation_study.py              # 自动化消融实验流水线
├── compare_models.py                  # 更新的模型比较(包含消融)
└── Ablation_Study_README.md           # 本文档
```

## 🚀 使用方法

### 方法1: 使用自动化流水线 (推荐)

```bash
# 运行完整消融实验流水线
python run_ablation_study.py --model-type both

# 只训练基础消融模型
python run_ablation_study.py --model-type basic

# 只训练多尺度消融模型
python run_ablation_study.py --model-type multiscale

# 跳过训练，只进行分析
python run_ablation_study.py --skip-training

# 跳过可视化
python run_ablation_study.py --skip-visualization
```

### 方法2: 分步执行

```bash
# 1. 训练消融模型
python train_sst_era5_convlstm.py

# 2. 可视化结果
python visualize_sst_era5_convlstm.py

# 3. 更新模型比较
python compare_models.py
```

### 前置条件

确保已完成数据预处理：
```bash
python preprocess_sst_pcs_era5.py
```

## 📈 预期结果

### 性能预期

基于PCs特征的理论重要性，预期：

1. **性能下降**: 消融模型性能应低于完整模型
2. **下降幅度**: 预期RMSE增加5-15%
3. **空间差异**: 某些区域可能受影响更大
4. **时间稳定性**: 消融模型可能在某些时期表现更差

### 分析指标

```python
# PCs贡献度计算
rmse_contribution = (ablation_rmse - full_rmse) / full_rmse * 100
mae_contribution = (ablation_mae - full_mae) / full_mae * 100
```

## 📊 输出结果

### 训练结果
- `sst_era5_ablation_results/best_*.pth`: 最佳消融模型权重
- `sst_era5_ablation_results/test_predictions_*.npy`: 消融模型预测结果

### 可视化结果
- `sst_era5_ablation_visualization/sst_comparison_*.png`: SST对比图
- `sst_era5_ablation_visualization/rmse_map_*.png`: RMSE空间分布
- `sst_era5_ablation_visualization/error_time_series_*.png`: 误差时间序列

### 分析报告
- `ablation_study_results/ablation_analysis_report.md`: 详细分析报告
- `model_comparison/performance_comparison.csv`: 更新的性能比较表

## 🔍 分析方法

### 1. 定量分析

```python
# 性能指标对比
完整模型_RMSE vs 消融模型_RMSE
完整模型_MAE vs 消融模型_MAE

# PCs贡献度
PCs_RMSE_贡献 = (消融RMSE - 完整RMSE) / 完整RMSE × 100%
PCs_MAE_贡献 = (消融MAE - 完整MAE) / 完整MAE × 100%
```

### 2. 定性分析

- **空间分布差异**: 哪些区域受PCs影响最大
- **时间变化模式**: PCs在不同时期的重要性
- **误差特征**: 消融模型的典型错误模式

### 3. 统计显著性

- **配对t检验**: 验证性能差异的统计显著性
- **置信区间**: 估计PCs贡献度的置信区间

## 💡 实验意义

### 科学价值

1. **特征重要性**: 量化PCs在SST预测中的作用
2. **模型解释**: 提高模型的可解释性
3. **设计指导**: 为未来模型设计提供依据

### 工程价值

1. **计算效率**: 评估简化模型的可行性
2. **资源优化**: 平衡性能与计算成本
3. **部署策略**: 为不同应用场景选择合适模型

## ⚠️ 注意事项

### 实验限制

1. **单一消融**: 只移除PCs，未考虑其他特征组合
2. **固定架构**: 模型架构保持不变，只改变输入
3. **数据依赖**: 结果可能依赖于特定数据集

### 解释谨慎

1. **因果关系**: 性能下降不一定完全归因于PCs
2. **交互效应**: PCs与其他特征可能存在交互作用
3. **泛化性**: 结果在其他数据集上的泛化性需验证

## 🔮 扩展方向

### 1. 多重消融

- 移除不同ERA5变量
- 移除不同数量的PCs
- 移除SST历史信息

### 2. 特征重要性排序

- 使用SHAP值分析
- 梯度归因方法
- 注意力权重分析

### 3. 架构消融

- 不同ConvLSTM层数
- 不同注意力机制
- 不同融合策略

## 📚 参考文献

1. **消融研究方法论**: Ablation studies in artificial neural networks
2. **特征重要性分析**: Feature importance in deep learning models
3. **海洋预测评估**: Evaluation metrics for ocean prediction models

---

*本消融实验为理解PCs特征在多源数据融合SST预测中的作用提供了系统性的分析框架，为海洋预测模型的优化和改进提供了科学依据。*
