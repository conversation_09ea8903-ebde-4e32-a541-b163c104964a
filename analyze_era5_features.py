#!/usr/bin/env python3
"""
ERA5数据特征分析脚本
分析ERA5数据中各变量的特征和与SST的相关性

作者: AI Assistant
日期: 2025-07-02
"""

import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import pandas as pd
from scipy.stats import pearsonr
from scipy.interpolate import griddata
import os

print("开始分析ERA5数据特征...")

# 创建结果文件夹
os.makedirs('era5_analysis', exist_ok=True)

# 读取数据
print("读取SST和ERA5数据...")
sst_data = xr.open_dataset('SST-V2.nc')
era5_data = xr.open_dataset('data_ERA5.nc')

print(f"SST数据形状: {sst_data.analysed_sst.shape}")
print(f"ERA5数据形状: {era5_data.dims}")
print(f"ERA5变量: {list(era5_data.data_vars.keys())}")

# 重命名时间维度
era5_data = era5_data.rename({'valid_time': 'time'})

# ERA5变量说明
era5_vars_info = {
    'u10': {'name': '10m风速u分量', 'unit': 'm/s', 'description': '10米高度东西向风速分量'},
    'v10': {'name': '10m风速v分量', 'unit': 'm/s', 'description': '10米高度南北向风速分量'},
    't2m': {'name': '2m温度', 'unit': 'K', 'description': '2米高度气温'},
    'msl': {'name': '海平面气压', 'unit': 'Pa', 'description': '海平面气压'}
}

print("\nERA5变量详细信息:")
for var, info in era5_vars_info.items():
    if var in era5_data.data_vars:
        data = era5_data[var]
        print(f"{var} ({info['name']}):")
        print(f"  单位: {info['unit']}")
        print(f"  描述: {info['description']}")
        print(f"  形状: {data.shape}")
        print(f"  范围: {data.min().values:.3f} - {data.max().values:.3f}")
        print(f"  平均值: {data.mean().values:.3f}")
        print()

# 插值ERA5数据到SST网格
def interpolate_era5_to_sst_grid(era5_ds, sst_ds, var_name):
    """将ERA5变量插值到SST网格"""
    print(f"插值 {var_name} 到SST网格...")
    
    # 创建原始经纬度网格点
    lon_era5, lat_era5 = np.meshgrid(era5_ds.longitude.values, era5_ds.latitude.values)
    
    # 创建目标经纬度网格点
    lon_sst, lat_sst = np.meshgrid(sst_ds.longitude.values, sst_ds.latitude.values)
    
    # 初始化结果数组
    result = np.zeros((len(era5_ds.time), len(sst_ds.latitude), len(sst_ds.longitude)))
    
    # 对每个时间点进行插值
    for t in range(len(era5_ds.time)):
        if t % 1000 == 0:
            print(f"  处理时间点: {t}/{len(era5_ds.time)}")
        
        # 获取ERA5当前时间的数据
        z = era5_ds[var_name].isel(time=t).values
        
        # 将原始经纬度网格和数据平铺
        points = np.column_stack((lon_era5.flatten(), lat_era5.flatten()))
        values = z.flatten()
        
        # 使用griddata进行插值
        result[t] = griddata(points, values, (lon_sst, lat_sst), method='linear')
    
    return result

# 插值所有ERA5变量到SST网格
print("插值所有ERA5变量到SST网格...")
era5_interpolated = {}
for var in era5_vars_info.keys():
    if var in era5_data.data_vars:
        era5_interpolated[var] = interpolate_era5_to_sst_grid(era5_data, sst_data, var)

# 获取SST数据
sst_values = sst_data.analysed_sst.values

# 确保时间维度匹配
min_time_len = min(len(sst_data.time), len(era5_data.time))
print(f"匹配时间长度: {min_time_len}")

# 截取匹配的时间长度
sst_matched = sst_values[:min_time_len]
for var in era5_interpolated:
    era5_interpolated[var] = era5_interpolated[var][:min_time_len]

print("计算ERA5变量与SST的相关性...")

# 计算空间平均的时间序列相关性
correlations = {}
for var in era5_interpolated:
    # 计算空间平均时间序列
    sst_spatial_mean = np.nanmean(sst_matched, axis=(1, 2))
    era5_spatial_mean = np.nanmean(era5_interpolated[var], axis=(1, 2))
    
    # 计算相关系数
    valid_mask = ~(np.isnan(sst_spatial_mean) | np.isnan(era5_spatial_mean))
    if np.sum(valid_mask) > 10:  # 确保有足够的有效数据点
        corr, p_value = pearsonr(sst_spatial_mean[valid_mask], era5_spatial_mean[valid_mask])
        correlations[var] = {'correlation': corr, 'p_value': p_value}
        print(f"{var} 与 SST 的相关系数: {corr:.4f} (p-value: {p_value:.4e})")

# 可视化ERA5变量的空间分布（选择中间时间点）
mid_time_idx = min_time_len // 2
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

# SST
im0 = axes[0].imshow(sst_matched[mid_time_idx] - 273.15, cmap='coolwarm',
                     extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                            sst_data.latitude.values.min(), sst_data.latitude.values.max()])
axes[0].set_title('SST (°C)')
axes[0].set_xlabel('经度')
axes[0].set_ylabel('纬度')
plt.colorbar(im0, ax=axes[0])

# ERA5变量
for i, (var, info) in enumerate(era5_vars_info.items(), 1):
    if var in era5_interpolated:
        data = era5_interpolated[var][mid_time_idx]
        if var == 't2m':
            data = data - 273.15  # 转换为摄氏度
            unit = '°C'
        else:
            unit = info['unit']
            
        im = axes[i].imshow(data, cmap='viridis',
                           extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                  sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[i].set_title(f"{info['name']} ({unit})")
        axes[i].set_xlabel('经度')
        axes[i].set_ylabel('纬度')
        plt.colorbar(im, ax=axes[i])

# 隐藏多余的子图
if len(era5_vars_info) < 4:
    axes[5].set_visible(False)

plt.tight_layout()
plt.savefig('era5_analysis/era5_spatial_distribution.png', dpi=300, bbox_inches='tight')
plt.close()

# 可视化时间序列相关性
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
axes = axes.flatten()

# 计算空间平均时间序列
sst_spatial_mean = np.nanmean(sst_matched, axis=(1, 2)) - 273.15  # 转换为摄氏度

for i, (var, info) in enumerate(era5_vars_info.items()):
    if var in era5_interpolated and i < 4:
        era5_spatial_mean = np.nanmean(era5_interpolated[var], axis=(1, 2))
        if var == 't2m':
            era5_spatial_mean = era5_spatial_mean - 273.15  # 转换为摄氏度
            unit = '°C'
        else:
            unit = info['unit']
        
        # 绘制时间序列
        time_indices = np.arange(len(sst_spatial_mean))
        ax1 = axes[i]
        ax2 = ax1.twinx()
        
        line1 = ax1.plot(time_indices, sst_spatial_mean, 'b-', label='SST (°C)', alpha=0.7)
        line2 = ax2.plot(time_indices, era5_spatial_mean, 'r-', label=f'{info["name"]} ({unit})', alpha=0.7)
        
        ax1.set_xlabel('时间索引')
        ax1.set_ylabel('SST (°C)', color='b')
        ax2.set_ylabel(f'{info["name"]} ({unit})', color='r')
        ax1.tick_params(axis='y', labelcolor='b')
        ax2.tick_params(axis='y', labelcolor='r')
        
        # 添加相关系数信息
        if var in correlations:
            corr = correlations[var]['correlation']
            ax1.set_title(f'{info["name"]} vs SST\n相关系数: {corr:.4f}')
        else:
            ax1.set_title(f'{info["name"]} vs SST')
        
        ax1.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('era5_analysis/era5_sst_time_series.png', dpi=300, bbox_inches='tight')
plt.close()

# 保存分析结果
print("保存分析结果...")
analysis_results = {
    'era5_vars_info': era5_vars_info,
    'correlations': correlations,
    'data_shapes': {
        'sst_shape': sst_matched.shape,
        'era5_shapes': {var: data.shape for var, data in era5_interpolated.items()},
        'matched_time_length': min_time_len
    }
}

# 保存插值后的ERA5数据
for var, data in era5_interpolated.items():
    np.save(f'era5_analysis/{var}_interpolated.npy', data)

# 保存匹配的SST数据
np.save('era5_analysis/sst_matched.npy', sst_matched)

# 生成分析报告
report = f"""
# ERA5数据特征分析报告

## 数据概况
- SST数据形状: {sst_matched.shape}
- ERA5数据时间长度: {min_time_len}
- 空间分辨率: {len(sst_data.latitude)} x {len(sst_data.longitude)}

## ERA5变量信息
"""

for var, info in era5_vars_info.items():
    if var in era5_interpolated:
        report += f"""
### {var} - {info['name']}
- 单位: {info['unit']}
- 描述: {info['description']}
- 数据范围: {era5_interpolated[var].min():.3f} - {era5_interpolated[var].max():.3f}
- 与SST相关系数: {correlations.get(var, {}).get('correlation', 'N/A')}
"""

report += f"""
## 相关性分析结果
"""
for var, corr_info in correlations.items():
    report += f"- {era5_vars_info[var]['name']}: {corr_info['correlation']:.4f} (p-value: {corr_info['p_value']:.4e})\n"

with open('era5_analysis/analysis_report.md', 'w', encoding='utf-8') as f:
    f.write(report)

print("ERA5数据特征分析完成！")
print("结果保存在 era5_analysis/ 文件夹中")

# 关闭数据集
sst_data.close()
era5_data.close()
