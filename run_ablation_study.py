#!/usr/bin/env python3
"""
SST+ERA5 ConvLSTM消融实验自动化流水线
自动执行消融实验的训练、可视化和性能比较

作者: AI Assistant
日期: 2025-07-02
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime
import numpy as np

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"开始执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {description} 完成！用时: {duration:.2f}秒")
        
        # 打印输出的最后几行
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            print("输出摘要:")
            for line in lines[-5:]:  # 显示最后5行
                print(f"  {line}")
                
        return True
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ {description} 失败！用时: {duration:.2f}秒")
        print(f"错误代码: {e.returncode}")
        print(f"错误信息: {e.stderr}")
        
        return False

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查消融实验前置条件...")
    
    required_files = [
        'sst_pcs_era5_data/sst_train_norm.npy',
        'sst_pcs_era5_data/sst_val_norm.npy',
        'sst_pcs_era5_data/sst_test_norm.npy',
        'sst_pcs_era5_data/u10_train_norm.npy',
        'sst_pcs_era5_data/v10_train_norm.npy',
        'sst_pcs_era5_data/t2m_train_norm.npy',
        'sst_pcs_era5_data/msl_train_norm.npy'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下必需文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        
        print("\n💡 请先运行数据预处理:")
        print("   python preprocess_sst_pcs_era5.py")
        
        return False
    
    print("✅ 所有前置条件满足")
    return True

def compare_ablation_results():
    """比较消融实验结果"""
    print("\n📊 分析消融实验结果...")
    
    try:
        # 加载完整模型结果
        full_model_file = 'sst_pcs_era5_results/test_predictions_sst_pcs_era5_basic.npy'
        ablation_model_file = 'sst_era5_ablation_results/test_predictions_sst_era5_ablation_basic.npy'
        targets_file = 'sst_era5_ablation_results/test_targets_sst_era5_ablation_basic.npy'
        
        if not all(os.path.exists(f) for f in [full_model_file, ablation_model_file, targets_file]):
            print("⚠️  缺少必要的结果文件，无法进行对比分析")
            return
        
        # 加载标准化参数
        sst_mean = np.load('sst_pcs_era5_data/sst_mean.npy')
        sst_std = np.load('sst_pcs_era5_data/sst_std.npy')
        
        # 加载预测结果
        full_pred = np.load(full_model_file)
        ablation_pred = np.load(ablation_model_file)
        targets = np.load(targets_file)
        
        # 反标准化并转换为摄氏度
        def process_data(data):
            denorm = data * sst_std + sst_mean
            return denorm - 273.15
        
        full_pred_celsius = process_data(full_pred)
        ablation_pred_celsius = process_data(ablation_pred)
        targets_celsius = process_data(targets)
        
        # 计算性能指标
        def calculate_metrics(pred, true):
            error = pred - true
            rmse = np.sqrt(np.mean(error**2))
            mae = np.mean(np.abs(error))
            return rmse, mae
        
        full_rmse, full_mae = calculate_metrics(full_pred_celsius, targets_celsius)
        ablation_rmse, ablation_mae = calculate_metrics(ablation_pred_celsius, targets_celsius)
        
        # 计算PCs贡献
        rmse_degradation = ablation_rmse - full_rmse
        mae_degradation = ablation_mae - full_mae
        
        rmse_contribution = (rmse_degradation / full_rmse) * 100
        mae_contribution = (mae_degradation / full_mae) * 100
        
        # 生成分析报告
        analysis_report = f"""
# 消融实验分析报告

## 实验设计
- **完整模型**: SST + PCs + ERA5 → SST
- **消融模型**: SST + ERA5 → SST (移除PCs特征)

## 性能对比

### RMSE (°C)
- 完整模型: {full_rmse:.4f}°C
- 消融模型: {ablation_rmse:.4f}°C
- 性能下降: {rmse_degradation:.4f}°C
- **PCs贡献度: {rmse_contribution:.2f}%**

### MAE (°C)
- 完整模型: {full_mae:.4f}°C
- 消融模型: {ablation_mae:.4f}°C
- 性能下降: {mae_degradation:.4f}°C
- **PCs贡献度: {mae_contribution:.2f}%**

## 结论
{'PCs特征对模型性能有显著贡献' if rmse_contribution > 5 else 'PCs特征对模型性能贡献有限'}

## 分析时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        # 保存分析报告
        os.makedirs('ablation_study_results', exist_ok=True)
        with open('ablation_study_results/ablation_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(analysis_report)
        
        print("✅ 消融实验分析完成")
        print(f"📈 PCs特征对RMSE的贡献: {rmse_contribution:.2f}%")
        print(f"📈 PCs特征对MAE的贡献: {mae_contribution:.2f}%")
        print("📄 详细报告保存在: ablation_study_results/ablation_analysis_report.md")
        
    except Exception as e:
        print(f"❌ 消融实验分析失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='SST+ERA5 ConvLSTM消融实验自动化流水线')
    parser.add_argument('--model-type', choices=['basic', 'multiscale', 'both'], 
                       default='basic', help='要训练的消融模型类型')
    parser.add_argument('--skip-training', action='store_true', 
                       help='跳过消融模型训练步骤')
    parser.add_argument('--skip-visualization', action='store_true', 
                       help='跳过结果可视化步骤')
    parser.add_argument('--skip-comparison', action='store_true', 
                       help='跳过模型比较步骤')
    
    args = parser.parse_args()
    
    print("🧪 SST+ERA5 ConvLSTM 消融实验流水线")
    print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 消融模型类型: {args.model_type}")
    print("🔬 实验目标: 量化PCs特征对SST预测性能的贡献")
    
    # 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件不满足，退出消融实验")
        return 1
    
    success_count = 0
    total_steps = 0
    
    # 步骤1: 消融模型训练
    if not args.skip_training:
        total_steps += 1
        print(f"\n🧠 步骤 {total_steps}: 消融模型训练")
        
        # 修改训练脚本中的模型类型
        train_command = f"python -c \"exec(open('train_sst_era5_convlstm.py').read().replace('model_type = \\\"basic\\\"', 'model_type = \\\"{args.model_type}\\\"))\""
        
        if run_command(train_command, f"训练{args.model_type}消融模型"):
            success_count += 1
        else:
            print("❌ 消融模型训练失败，但继续执行后续步骤")
    else:
        print("\n⏭️  跳过消融模型训练步骤")
    
    # 步骤2: 结果可视化
    if not args.skip_visualization:
        total_steps += 1
        print(f"\n📈 步骤 {total_steps}: 消融实验结果可视化")
        if run_command("python visualize_sst_era5_convlstm.py", "消融实验结果可视化"):
            success_count += 1
        else:
            print("❌ 结果可视化失败，但继续执行后续步骤")
    else:
        print("\n⏭️  跳过结果可视化步骤")
    
    # 步骤3: 消融实验分析
    total_steps += 1
    print(f"\n🔍 步骤 {total_steps}: 消融实验分析")
    compare_ablation_results()
    success_count += 1
    
    # 步骤4: 模型比较
    if not args.skip_comparison:
        total_steps += 1
        print(f"\n📊 步骤 {total_steps}: 更新模型性能比较")
        if run_command("python compare_models.py", "模型性能比较(包含消融实验)"):
            success_count += 1
        else:
            print("❌ 模型比较失败")
    else:
        print("\n⏭️  跳过模型比较步骤")
    
    # 总结
    print(f"\n{'='*60}")
    print("🎉 消融实验流水线执行完成！")
    print(f"📅 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"✅ 成功步骤: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("🎊 所有步骤都成功完成！")
        
        print("\n📁 输出文件位置:")
        print("  - 消融模型结果: sst_era5_ablation_results/")
        print("  - 消融实验图表: sst_era5_ablation_visualization/")
        print("  - 分析报告: ablation_study_results/")
        print("  - 模型比较: model_comparison/")
        
        print("\n🔍 主要结果文件:")
        print("  - 消融模型权重: sst_era5_ablation_results/best_sst_era5_ablation_*.pth")
        print("  - 消融实验预测: sst_era5_ablation_results/test_predictions_*.npy")
        print("  - 分析报告: ablation_study_results/ablation_analysis_report.md")
        print("  - 性能比较: model_comparison/performance_comparison.csv")
        
        return 0
    else:
        print(f"⚠️  有 {total_steps - success_count} 个步骤失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
