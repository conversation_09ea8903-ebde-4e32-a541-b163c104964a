#!/usr/bin/env python3
"""
数据形状调试脚本
用于诊断和解决PCs形状与模型输入要求不一致的问题

作者: AI Assistant
日期: 2025-06-28
"""

import numpy as np
import xarray as xr
import torch
from torch.utils.data import Dataset, DataLoader
import os

def check_data_files():
    """检查数据文件是否存在和基本信息"""
    print("🔍 检查数据文件...")
    
    files_to_check = [
        'SST-V2.nc',
        'results/PCs_train.npy',
        'results/PCs_val.npy', 
        'results/PCs_test.npy',
        'results/sst_mean.npy',
        'results/sst_std.npy'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
            if file_path.endswith('.npy'):
                data = np.load(file_path)
                print(f"   形状: {data.shape}, 数据类型: {data.dtype}")
                print(f"   范围: {data.min():.6f} - {data.max():.6f}")
            elif file_path.endswith('.nc'):
                with xr.open_dataset(file_path) as ds:
                    print(f"   变量: {list(ds.data_vars.keys())}")
                    if 'analysed_sst' in ds:
                        sst = ds.analysed_sst
                        print(f"   SST形状: {sst.shape}")
                        print(f"   时间范围: {sst.time.values[0]} - {sst.time.values[-1]}")
        else:
            print(f"❌ {file_path} 不存在")
    print()

def analyze_data_dimensions():
    """分析数据维度匹配情况"""
    print("📊 分析数据维度...")
    
    # 加载SST数据
    sst_data = xr.open_dataset('SST-V2.nc')
    sst_values = sst_data.analysed_sst.values
    n_time_total, n_lat, n_lon = sst_values.shape
    
    print(f"SST原始数据形状: {sst_values.shape}")
    print(f"时间点数: {n_time_total}, 纬度: {n_lat}, 经度: {n_lon}")
    
    # 数据集划分
    train_end = int(n_time_total * 0.7)
    val_end = int(n_time_total * 0.9)
    
    sst_train_len = train_end
    sst_val_len = val_end - train_end
    sst_test_len = n_time_total - val_end
    
    print(f"SST数据划分:")
    print(f"  训练集: 0 - {train_end} ({sst_train_len} 个时间点)")
    print(f"  验证集: {train_end} - {val_end} ({sst_val_len} 个时间点)")
    print(f"  测试集: {val_end} - {n_time_total} ({sst_test_len} 个时间点)")
    
    # 加载PCs数据
    PCs_train = np.load('results/PCs_train.npy')
    PCs_val = np.load('results/PCs_val.npy')
    PCs_test = np.load('results/PCs_test.npy')
    
    k, pc_train_len = PCs_train.shape
    _, pc_val_len = PCs_val.shape
    _, pc_test_len = PCs_test.shape
    
    print(f"\nPCs数据形状:")
    print(f"  训练集: {PCs_train.shape} (k={k}, 时间={pc_train_len})")
    print(f"  验证集: {PCs_val.shape} (时间={pc_val_len})")
    print(f"  测试集: {PCs_test.shape} (时间={pc_test_len})")
    
    # 检查匹配情况
    print(f"\n🔍 时间维度匹配检查:")
    print(f"训练集: SST={sst_train_len} vs PCs={pc_train_len} {'✅' if sst_train_len == pc_train_len else '❌'}")
    print(f"验证集: SST={sst_val_len} vs PCs={pc_val_len} {'✅' if sst_val_len == pc_val_len else '❌'}")
    print(f"测试集: SST={sst_test_len} vs PCs={pc_test_len} {'✅' if sst_test_len == pc_test_len else '❌'}")
    
    return {
        'sst_shape': sst_values.shape,
        'k': k,
        'train_len': min(sst_train_len, pc_train_len),
        'val_len': min(sst_val_len, pc_val_len),
        'test_len': min(sst_test_len, pc_test_len),
        'n_lat': n_lat,
        'n_lon': n_lon
    }

def test_dataset_creation(info):
    """测试数据集创建"""
    print("🧪 测试数据集创建...")
    
    # 模拟数据
    seq_len, pred_len = 14, 1
    k = info['k']
    n_lat, n_lon = info['n_lat'], info['n_lon']
    train_len = info['train_len']
    
    print(f"参数: seq_len={seq_len}, pred_len={pred_len}, k={k}")
    print(f"空间维度: {n_lat}x{n_lon}")
    
    # 创建模拟数据
    sst_data = np.random.randn(train_len, n_lat, n_lon).astype(np.float32)
    pcs_data = np.random.randn(k, train_len).astype(np.float32)
    
    print(f"模拟SST数据形状: {sst_data.shape}")
    print(f"模拟PCs数据形状: {pcs_data.shape}")
    
    # 测试数据集类
    class TestSSTWithPCsDataset(Dataset):
        def __init__(self, sst_data, pcs_data, seq_len, pred_len):
            self.sst_data = sst_data
            self.pcs_data = pcs_data
            self.seq_len = seq_len
            self.pred_len = pred_len
            self.total_len = seq_len + pred_len
            
            self.n_lat, self.n_lon = sst_data.shape[1], sst_data.shape[2]
            self.k = pcs_data.shape[0]
            
            assert sst_data.shape[0] == pcs_data.shape[1], f"时间维度不匹配: {sst_data.shape[0]} vs {pcs_data.shape[1]}"
            
        def __len__(self):
            return self.sst_data.shape[0] - self.total_len + 1
        
        def __getitem__(self, idx):
            # SST数据
            sst_x = self.sst_data[idx:idx+self.seq_len]
            sst_y = self.sst_data[idx+self.seq_len:idx+self.total_len]
            
            # PCs数据
            pcs_x = self.pcs_data[:, idx:idx+self.seq_len].T  # (seq_len, k)
            
            # PCs空间扩展
            pcs_spatial = np.tile(
                pcs_x[:, :, np.newaxis, np.newaxis], 
                (1, 1, self.n_lat, self.n_lon)
            )
            
            # 合并数据
            sst_expanded = sst_x[:, np.newaxis, :, :]  # (seq_len, 1, lat, lon)
            combined_input = np.concatenate([sst_expanded, pcs_spatial], axis=1)
            
            return torch.FloatTensor(combined_input), torch.FloatTensor(sst_y)
    
    try:
        dataset = TestSSTWithPCsDataset(sst_data, pcs_data, seq_len, pred_len)
        print(f"✅ 数据集创建成功，样本数: {len(dataset)}")
        
        # 测试获取样本
        sample_input, sample_target = dataset[0]
        print(f"✅ 样本获取成功")
        print(f"   输入形状: {sample_input.shape}")
        print(f"   目标形状: {sample_target.shape}")
        
        expected_input_shape = (seq_len, 1 + k, n_lat, n_lon)
        expected_target_shape = (pred_len, n_lat, n_lon)
        
        if sample_input.shape == expected_input_shape:
            print(f"✅ 输入形状正确: {sample_input.shape}")
        else:
            print(f"❌ 输入形状错误: 期望 {expected_input_shape}, 实际 {sample_input.shape}")
            
        if sample_target.shape == expected_target_shape:
            print(f"✅ 目标形状正确: {sample_target.shape}")
        else:
            print(f"❌ 目标形状错误: 期望 {expected_target_shape}, 实际 {sample_target.shape}")
        
        # 测试DataLoader
        dataloader = DataLoader(dataset, batch_size=2, shuffle=False)
        batch_input, batch_target = next(iter(dataloader))
        print(f"✅ DataLoader测试成功")
        print(f"   批次输入形状: {batch_input.shape}")
        print(f"   批次目标形状: {batch_target.shape}")
        
        # 内存使用估算
        memory_per_sample = sample_input.numel() * 4 / (1024**2)  # MB
        print(f"📊 每样本内存使用: {memory_per_sample:.2f} MB")
        
        total_memory = memory_per_sample * len(dataset)
        print(f"📊 总内存需求: {total_memory:.2f} MB ({total_memory/1024:.2f} GB)")
        
        if total_memory > 1024:  # 超过1GB
            print("⚠️ 警告: 内存需求较大，建议使用内存优化版本")
        
    except Exception as e:
        print(f"❌ 数据集测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("=" * 80)
    print("🔧 PCs形状与模型输入要求不一致问题诊断")
    print("=" * 80)
    
    # 检查文件
    check_data_files()
    
    # 分析维度
    info = analyze_data_dimensions()
    
    # 测试数据集
    test_dataset_creation(info)
    
    print("\n" + "=" * 80)
    print("🎯 问题解决建议:")
    print("1. 确保SST和PCs数据的时间维度匹配")
    print("2. 使用内存优化的数据集类处理大数据")
    print("3. 根据内存情况调整批次大小")
    print("4. 检查数据类型和数值范围")
    print("5. 使用适当的错误处理和调试信息")
    print("=" * 80)

if __name__ == "__main__":
    main()
