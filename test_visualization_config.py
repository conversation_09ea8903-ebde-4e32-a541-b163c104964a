#!/usr/bin/env python3
"""
测试统一的可视化配置
验证颜色方案、范围设置和日期功能是否正常工作

作者: AI Assistant
日期: 2025-07-02
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False
import os

# 测试导入可视化配置
try:
    from visualization_config import (
        SST_COLORMAP, SST_VMIN, SST_VMAX,
        ERROR_COLORMAP, ERROR_VMIN, ERROR_VMAX,
        RMSE_COLORMAP, RMSE_VMIN, RMSE_VMAX,
        TIMESERIES_COLORS, FIGURE_SIZES, FONT_SIZES, DPI,
        get_sample_date_mapping, format_date_for_title,
        get_adaptive_sst_range, get_adaptive_error_range
    )
    print("✅ 可视化配置导入成功")
except ImportError as e:
    print(f"❌ 可视化配置导入失败: {e}")
    exit(1)

# 创建测试结果文件夹
os.makedirs('visualization_test', exist_ok=True)

print("🧪 开始测试可视化配置...")

# 测试1: 颜色方案和范围
print("\n📊 测试1: 颜色方案和范围设置")

# 生成测试数据
np.random.seed(42)
sst_data = np.random.uniform(SST_VMIN, SST_VMAX, (100, 140))
error_data = np.random.uniform(ERROR_VMIN, ERROR_VMAX, (100, 140))
rmse_data = np.random.uniform(RMSE_VMIN, RMSE_VMAX, (100, 140))

# 测试SST颜色方案
fig, axes = plt.subplots(1, 3, figsize=FIGURE_SIZES['triple_comparison'])

# SST场
im1 = axes[0].imshow(sst_data, cmap=SST_COLORMAP, vmin=SST_VMIN, vmax=SST_VMAX)
axes[0].set_title('SST场测试', fontsize=FONT_SIZES['title'])
axes[0].set_xlabel('经度', fontsize=FONT_SIZES['label'])
axes[0].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
cbar1 = plt.colorbar(im1, ax=axes[0])
cbar1.set_label('SST (°C)', fontsize=FONT_SIZES['label'])

# 误差场
im2 = axes[1].imshow(error_data, cmap=ERROR_COLORMAP, vmin=ERROR_VMIN, vmax=ERROR_VMAX)
axes[1].set_title('误差场测试', fontsize=FONT_SIZES['title'])
axes[1].set_xlabel('经度', fontsize=FONT_SIZES['label'])
axes[1].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
cbar2 = plt.colorbar(im2, ax=axes[1])
cbar2.set_label('误差 (°C)', fontsize=FONT_SIZES['label'])

# RMSE场
im3 = axes[2].imshow(rmse_data, cmap=RMSE_COLORMAP, vmin=RMSE_VMIN, vmax=RMSE_VMAX)
axes[2].set_title('RMSE场测试', fontsize=FONT_SIZES['title'])
axes[2].set_xlabel('经度', fontsize=FONT_SIZES['label'])
axes[2].set_ylabel('纬度', fontsize=FONT_SIZES['label'])
cbar3 = plt.colorbar(im3, ax=axes[2])
cbar3.set_label('RMSE (°C)', fontsize=FONT_SIZES['label'])

plt.tight_layout()
plt.savefig('visualization_test/color_scheme_test.png', dpi=DPI, bbox_inches='tight')
plt.close()

print(f"  ✅ SST颜色方案: {SST_COLORMAP}, 范围: {SST_VMIN}-{SST_VMAX}°C")
print(f"  ✅ 误差颜色方案: {ERROR_COLORMAP}, 范围: {ERROR_VMIN}-{ERROR_VMAX}°C")
print(f"  ✅ RMSE颜色方案: {RMSE_COLORMAP}, 范围: {RMSE_VMIN}-{RMSE_VMAX}°C")

# 测试2: 时间序列颜色
print("\n📈 测试2: 时间序列颜色方案")

time_steps = np.arange(100)
true_values = 25 + 2 * np.sin(time_steps * 0.1) + np.random.normal(0, 0.5, 100)
pred_values = true_values + np.random.normal(0, 0.3, 100)
error_values = pred_values - true_values
rmse_values = np.sqrt(np.cumsum(error_values**2) / (time_steps + 1))

fig, (ax1, ax2) = plt.subplots(2, 1, figsize=FIGURE_SIZES['time_series'])

# 时间序列对比
ax1.plot(time_steps, true_values, label='真实值', 
         color=TIMESERIES_COLORS['true'], alpha=0.8, linewidth=1.5)
ax1.plot(time_steps, pred_values, label='预测值', 
         color=TIMESERIES_COLORS['predicted'], alpha=0.8, linewidth=1.5)
ax1.set_title('时间序列对比测试', fontsize=FONT_SIZES['title'])
ax1.set_ylabel('SST (°C)', fontsize=FONT_SIZES['label'])
ax1.legend(fontsize=FONT_SIZES['legend'])
ax1.grid(True, alpha=0.3)
ax1.tick_params(labelsize=FONT_SIZES['tick'])

# 误差和RMSE
ax2.plot(time_steps, error_values, label='误差', 
         color=TIMESERIES_COLORS['error'], alpha=0.7, linewidth=1.0)
ax2.plot(time_steps, rmse_values, label='RMSE', 
         color=TIMESERIES_COLORS['rmse'], alpha=0.8, linewidth=1.5)
ax2.set_title('误差时间序列测试', fontsize=FONT_SIZES['title'])
ax2.set_xlabel('时间步', fontsize=FONT_SIZES['label'])
ax2.set_ylabel('误差 (°C)', fontsize=FONT_SIZES['label'])
ax2.legend(fontsize=FONT_SIZES['legend'])
ax2.grid(True, alpha=0.3)
ax2.tick_params(labelsize=FONT_SIZES['tick'])

plt.tight_layout()
plt.savefig('visualization_test/timeseries_color_test.png', dpi=DPI, bbox_inches='tight')
plt.close()

print(f"  ✅ 真实值颜色: {TIMESERIES_COLORS['true']}")
print(f"  ✅ 预测值颜色: {TIMESERIES_COLORS['predicted']}")
print(f"  ✅ 误差颜色: {TIMESERIES_COLORS['error']}")
print(f"  ✅ RMSE颜色: {TIMESERIES_COLORS['rmse']}")

# 测试3: 日期映射功能
print("\n📅 测试3: 日期映射功能")

try:
    # 测试日期映射
    date_mapping = get_sample_date_mapping(10, seq_len=14)
    print(f"  ✅ 日期映射生成成功，样本数: {len(date_mapping)}")
    
    # 显示前5个日期
    for i in range(min(5, len(date_mapping))):
        print(f"    样本{i}: {date_mapping[i]}")
    
    # 测试日期格式化
    test_date = format_date_for_title('2020-01-15')
    print(f"  ✅ 日期格式化测试: {test_date}")
    
except Exception as e:
    print(f"  ❌ 日期功能测试失败: {e}")

# 测试4: 自适应范围功能
print("\n🔧 测试4: 自适应范围功能")

try:
    # 测试自适应SST范围
    test_sst = np.random.uniform(18, 35, (50, 50))
    adaptive_sst_range = get_adaptive_sst_range(test_sst)
    print(f"  ✅ 自适应SST范围: {adaptive_sst_range[0]:.2f} - {adaptive_sst_range[1]:.2f}°C")
    
    # 测试自适应误差范围
    test_error = np.random.uniform(-3, 3, (50, 50))
    adaptive_error_range = get_adaptive_error_range(test_error)
    print(f"  ✅ 自适应误差范围: {adaptive_error_range[0]:.2f} - {adaptive_error_range[1]:.2f}°C")
    
except Exception as e:
    print(f"  ❌ 自适应范围功能测试失败: {e}")

# 测试5: 图表尺寸配置
print("\n📏 测试5: 图表尺寸配置")

print(f"  ✅ 三联对比图尺寸: {FIGURE_SIZES['triple_comparison']}")
print(f"  ✅ 单个地图尺寸: {FIGURE_SIZES['single_map']}")
print(f"  ✅ 时间序列尺寸: {FIGURE_SIZES['time_series']}")
print(f"  ✅ 性能对比尺寸: {FIGURE_SIZES['performance_bar']}")

# 测试6: 字体配置
print("\n🔤 测试6: 字体配置")

print(f"  ✅ 标题字体大小: {FONT_SIZES['title']}")
print(f"  ✅ 标签字体大小: {FONT_SIZES['label']}")
print(f"  ✅ 图例字体大小: {FONT_SIZES['legend']}")
print(f"  ✅ 刻度字体大小: {FONT_SIZES['tick']}")
print(f"  ✅ DPI设置: {DPI}")

# 生成配置总结
print("\n📋 生成配置总结...")

summary_content = f"""# 可视化配置测试总结

## 测试时间
{format_date_for_title('2025-07-02')}

## 颜色方案测试结果
- **SST场**: {SST_COLORMAP}色图, 范围: {SST_VMIN}-{SST_VMAX}°C
- **误差场**: {ERROR_COLORMAP}色图, 范围: {ERROR_VMIN}-{ERROR_VMAX}°C  
- **RMSE场**: {RMSE_COLORMAP}色图, 范围: {RMSE_VMIN}-{RMSE_VMAX}°C

## 时间序列颜色
- **真实值**: {TIMESERIES_COLORS['true']}
- **预测值**: {TIMESERIES_COLORS['predicted']}
- **误差**: {TIMESERIES_COLORS['error']}
- **RMSE**: {TIMESERIES_COLORS['rmse']}

## 图表尺寸配置
- **三联对比图**: {FIGURE_SIZES['triple_comparison']}
- **单个地图**: {FIGURE_SIZES['single_map']}
- **时间序列**: {FIGURE_SIZES['time_series']}
- **性能对比**: {FIGURE_SIZES['performance_bar']}

## 字体配置
- **标题**: {FONT_SIZES['title']}pt
- **标签**: {FONT_SIZES['label']}pt
- **图例**: {FONT_SIZES['legend']}pt
- **刻度**: {FONT_SIZES['tick']}pt

## 其他设置
- **DPI**: {DPI}
- **日期映射**: 自动生成样本日期
- **自适应范围**: 支持数据驱动的颜色范围

## 测试文件
- `color_scheme_test.png`: 颜色方案测试
- `timeseries_color_test.png`: 时间序列颜色测试

所有测试通过 ✅
"""

with open('visualization_test/test_summary.md', 'w', encoding='utf-8') as f:
    f.write(summary_content)

print("✅ 配置总结已保存到 visualization_test/test_summary.md")

print("\n🎉 可视化配置测试完成！")
print("\n📁 测试结果文件:")
print("  - visualization_test/color_scheme_test.png")
print("  - visualization_test/timeseries_color_test.png") 
print("  - visualization_test/test_summary.md")

print("\n🎨 统一可视化配置已成功应用到所有脚本！")
print("现在所有可视化图表将具有:")
print("  ✅ 统一的颜色方案和范围")
print("  ✅ 标准化的图表尺寸和字体")
print("  ✅ 自动的日期标注")
print("  ✅ 一致的视觉风格")
