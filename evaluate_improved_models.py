#!/usr/bin/env python3
"""
改进模型测试集评估和可视化脚本
详细分析改进模型在测试集上的性能表现

作者: AI Assistant
日期: 2025-07-02
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import pandas as pd
import xarray as xr
import os
from datetime import datetime
import seaborn as sns
from scipy import stats
from tqdm import tqdm

print("开始改进模型测试集评估和可视化...")

# 创建结果文件夹
os.makedirs('improved_model_evaluation', exist_ok=True)

# 加载标准化参数
print("加载标准化参数...")
sst_mean = np.load('sst_pcs_era5_data/sst_mean.npy')
sst_std = np.load('sst_pcs_era5_data/sst_std.npy')

print(f"SST标准化参数: mean={sst_mean:.3f}, std={sst_std:.3f}")

def denormalize_sst(sst_norm):
    """反标准化SST数据"""
    return sst_norm * sst_std + sst_mean

def kelvin_to_celsius(temp_k):
    """开尔文转摄氏度"""
    return temp_k - 273.15

# 定义模型信息
models_info = {
    'original_full': {
        'name': '原始完整模型 (SST+PCs+ERA5)',
        'file': 'sst_pcs_era5_results/test_predictions_sst_pcs_era5_basic.npy',
        'target_file': 'sst_pcs_era5_results/test_targets_sst_pcs_era5_basic.npy',
        'color': 'blue'
    },
    'ablation': {
        'name': '消融模型 (SST+ERA5)',
        'file': 'sst_era5_ablation_results/test_predictions_sst_era5_ablation_basic.npy',
        'target_file': 'sst_era5_ablation_results/test_targets_sst_era5_ablation_basic.npy',
        'color': 'red'
    },
    'improved_selective_pcs': {
        'name': '改进模型-选择性PCs',
        'file': 'improved_sst_pcs_era5_results/test_predictions_improved_selective_pcs.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_selective_pcs.npy',
        'color': 'green'
    },
    'improved_weighted_fusion': {
        'name': '改进模型-加权融合',
        'file': 'improved_sst_pcs_era5_results/test_predictions_improved_weighted_fusion.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_weighted_fusion.npy',
        'color': 'orange'
    },
    'improved_hierarchical_fusion': {
        'name': '改进模型-分层融合',
        'file': 'improved_sst_pcs_era5_results/test_predictions_improved_hierarchical_fusion.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_hierarchical_fusion.npy',
        'color': 'purple'
    }
}

# 加载所有模型的预测结果
print("加载所有模型的预测结果...")
models_data = {}
targets_celsius = None

for model_key, model_info in models_info.items():
    try:
        predictions = np.load(model_info['file'])
        targets = np.load(model_info['target_file'])
        
        # 反标准化并转换为摄氏度
        predictions_denorm = denormalize_sst(predictions)
        targets_denorm = denormalize_sst(targets)
        
        predictions_celsius = kelvin_to_celsius(predictions_denorm)
        targets_celsius_current = kelvin_to_celsius(targets_denorm)
        
        # 存储数据
        models_data[model_key] = {
            'predictions': predictions_celsius,
            'targets': targets_celsius_current,
            'info': model_info
        }
        
        # 使用第一个模型的目标作为基准
        if targets_celsius is None:
            targets_celsius = targets_celsius_current
        
        print(f"✅ 加载 {model_info['name']} 成功")
        
    except FileNotFoundError:
        print(f"⚠️  未找到 {model_info['name']} 的结果文件，跳过")
        continue

if not models_data:
    print("❌ 未找到任何模型结果文件")
    exit(1)

print(f"成功加载 {len(models_data)} 个模型的结果")

# 计算详细评估指标
def calculate_detailed_metrics(pred, true):
    """计算详细的评估指标"""
    error = pred - true
    
    metrics = {
        'rmse': np.sqrt(np.mean(error**2)),
        'mae': np.mean(np.abs(error)),
        'bias': np.mean(error),
        'std_error': np.std(error),
        'max_error': np.max(np.abs(error)),
        'r2': stats.pearsonr(pred.flatten(), true.flatten())[0]**2,
        'correlation': stats.pearsonr(pred.flatten(), true.flatten())[0]
    }
    
    return metrics

# 计算所有模型的详细指标
print("\n计算详细评估指标...")
all_metrics = {}

for model_key, data in models_data.items():
    metrics = calculate_detailed_metrics(data['predictions'], data['targets'])
    all_metrics[model_key] = metrics
    
    print(f"\n{data['info']['name']}:")
    print(f"  RMSE: {metrics['rmse']:.4f}°C")
    print(f"  MAE: {metrics['mae']:.4f}°C")
    print(f"  偏差: {metrics['bias']:.4f}°C")
    print(f"  相关系数: {metrics['correlation']:.4f}")
    print(f"  R²: {metrics['r2']:.4f}")

# 创建性能对比表
print("\n创建性能对比表...")
comparison_data = []
for model_key, metrics in all_metrics.items():
    model_info = models_data[model_key]['info']
    comparison_data.append({
        '模型': model_info['name'],
        'RMSE (°C)': f"{metrics['rmse']:.4f}",
        'MAE (°C)': f"{metrics['mae']:.4f}",
        '偏差 (°C)': f"{metrics['bias']:.4f}",
        '相关系数': f"{metrics['correlation']:.4f}",
        'R²': f"{metrics['r2']:.4f}",
        '最大误差 (°C)': f"{metrics['max_error']:.4f}"
    })

df_comparison = pd.DataFrame(comparison_data)
df_comparison.to_csv('improved_model_evaluation/detailed_performance_comparison.csv', 
                    index=False, encoding='utf-8-sig')
print("✅ 性能对比表已保存")

# 1. 绘制性能对比柱状图
print("\n绘制性能对比图...")
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

metrics_to_plot = ['rmse', 'mae', 'bias', 'correlation']
metric_names = ['RMSE (°C)', 'MAE (°C)', '偏差 (°C)', '相关系数']

for i, (metric, name) in enumerate(zip(metrics_to_plot, metric_names)):
    ax = axes[i//2, i%2]
    
    model_names = [models_data[key]['info']['name'] for key in all_metrics.keys()]
    values = [all_metrics[key][metric] for key in all_metrics.keys()]
    colors = [models_data[key]['info']['color'] for key in all_metrics.keys()]
    
    bars = ax.bar(range(len(model_names)), values, color=colors, alpha=0.7)
    ax.set_xlabel('模型')
    ax.set_ylabel(name)
    ax.set_title(f'模型{name}对比')
    ax.set_xticks(range(len(model_names)))
    ax.set_xticklabels([name.replace('改进模型-', '') for name in model_names], 
                       rotation=45, ha='right')
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
                f'{value:.4f}', ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.savefig('improved_model_evaluation/performance_comparison_bars.png', 
           dpi=300, bbox_inches='tight')
plt.close()

# 2. 绘制误差分布对比
print("绘制误差分布对比...")
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

for i, (model_key, data) in enumerate(models_data.items()):
    if i >= 6:  # 最多显示6个模型
        break
        
    error = data['predictions'] - data['targets']
    
    ax = axes[i]
    ax.hist(error.flatten(), bins=50, alpha=0.7, color=data['info']['color'], 
           density=True, label=data['info']['name'])
    ax.axvline(0, color='red', linestyle='--', alpha=0.8)
    ax.set_xlabel('预测误差 (°C)')
    ax.set_ylabel('密度')
    ax.set_title(f"{data['info']['name']}\n误差分布")
    ax.grid(True, alpha=0.3)
    
    # 添加统计信息
    rmse = all_metrics[model_key]['rmse']
    bias = all_metrics[model_key]['bias']
    ax.text(0.05, 0.95, f'RMSE: {rmse:.4f}°C\n偏差: {bias:.4f}°C', 
           transform=ax.transAxes, verticalalignment='top',
           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

# 隐藏多余的子图
for i in range(len(models_data), 6):
    axes[i].set_visible(False)

plt.tight_layout()
plt.savefig('improved_model_evaluation/error_distribution_comparison.png', 
           dpi=300, bbox_inches='tight')
plt.close()

# 3. 绘制散点图对比
print("绘制预测vs真实值散点图...")
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

for i, (model_key, data) in enumerate(models_data.items()):
    if i >= 6:
        break
        
    ax = axes[i]
    
    # 随机采样以减少绘图点数
    n_samples = min(5000, len(data['predictions'].flatten()))
    indices = np.random.choice(len(data['predictions'].flatten()), n_samples, replace=False)
    
    pred_sample = data['predictions'].flatten()[indices]
    true_sample = data['targets'].flatten()[indices]
    
    ax.scatter(true_sample, pred_sample, alpha=0.5, s=1, color=data['info']['color'])
    
    # 绘制理想线
    min_val = min(true_sample.min(), pred_sample.min())
    max_val = max(true_sample.max(), pred_sample.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
    
    ax.set_xlabel('真实SST (°C)')
    ax.set_ylabel('预测SST (°C)')
    ax.set_title(f"{data['info']['name']}\n预测vs真实")
    ax.grid(True, alpha=0.3)
    
    # 添加R²信息
    r2 = all_metrics[model_key]['r2']
    ax.text(0.05, 0.95, f'R² = {r2:.4f}', 
           transform=ax.transAxes, verticalalignment='top',
           bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

# 隐藏多余的子图
for i in range(len(models_data), 6):
    axes[i].set_visible(False)

plt.tight_layout()
plt.savefig('improved_model_evaluation/scatter_comparison.png', 
           dpi=300, bbox_inches='tight')
plt.close()

# 4. 绘制空间RMSE分布对比
print("绘制空间RMSE分布对比...")
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.flatten()

for i, (model_key, data) in enumerate(models_data.items()):
    if i >= 6:
        break
        
    # 计算空间RMSE
    error = data['predictions'] - data['targets']
    spatial_rmse = np.sqrt(np.mean(error**2, axis=0))  # [1, lat, lon]
    
    ax = axes[i]
    im = ax.imshow(spatial_rmse[0], cmap='viridis', aspect='auto')
    ax.set_title(f"{data['info']['name']}\n空间RMSE分布")
    ax.set_xlabel('经度索引')
    ax.set_ylabel('纬度索引')
    plt.colorbar(im, ax=ax, label='RMSE (°C)')

# 隐藏多余的子图
for i in range(len(models_data), 6):
    axes[i].set_visible(False)

plt.tight_layout()
plt.savefig('improved_model_evaluation/spatial_rmse_comparison.png', 
           dpi=300, bbox_inches='tight')
plt.close()

# 5. 时间序列性能对比
print("绘制时间序列性能对比...")
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

for model_key, data in models_data.items():
    # 计算空间平均时间序列
    spatial_mean_pred = np.mean(data['predictions'], axis=(1, 2, 3))
    spatial_mean_true = np.mean(data['targets'], axis=(1, 2, 3))
    
    # 计算时间序列RMSE
    time_rmse = np.sqrt(np.mean((data['predictions'] - data['targets'])**2, axis=(1, 2, 3)))
    
    # 绘制空间平均时间序列
    time_indices = np.arange(len(spatial_mean_pred))
    ax1.plot(time_indices, spatial_mean_pred, 
            label=f"{data['info']['name']} (预测)", 
            color=data['info']['color'], alpha=0.7)

# 绘制真实值（只需要一次）
first_model = list(models_data.values())[0]
spatial_mean_true = np.mean(first_model['targets'], axis=(1, 2, 3))
ax1.plot(time_indices, spatial_mean_true, 'k-', label='真实值', linewidth=2)

ax1.set_xlabel('时间步')
ax1.set_ylabel('空间平均SST (°C)')
ax1.set_title('空间平均SST时间序列对比')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 绘制时间序列RMSE
for model_key, data in models_data.items():
    time_rmse = np.sqrt(np.mean((data['predictions'] - data['targets'])**2, axis=(1, 2, 3)))
    ax2.plot(time_indices, time_rmse, 
            label=data['info']['name'], 
            color=data['info']['color'], alpha=0.7)

ax2.set_xlabel('时间步')
ax2.set_ylabel('RMSE (°C)')
ax2.set_title('时间序列RMSE对比')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('improved_model_evaluation/time_series_comparison.png', 
           dpi=300, bbox_inches='tight')
plt.close()

print("\n✅ 所有可视化图表已生成完成！")
print("📁 结果保存在 improved_model_evaluation/ 文件夹中")
print("📊 主要输出文件:")
print("  - detailed_performance_comparison.csv: 详细性能对比表")
print("  - performance_comparison_bars.png: 性能对比柱状图")
print("  - error_distribution_comparison.png: 误差分布对比")
print("  - scatter_comparison.png: 预测vs真实值散点图")
print("  - spatial_rmse_comparison.png: 空间RMSE分布对比")
print("  - time_series_comparison.png: 时间序列性能对比")
