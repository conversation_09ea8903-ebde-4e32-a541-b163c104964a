import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # Set font that supports Chinese
rcParams['axes.unicode_minus'] = False    # Fix negative sign display
from mpl_toolkits.basemap import Basemap

# 地图边界
lonmin = 105
lonmax = 124
latmin = 12
latmax = 25

# 指定区域的边界
target_lon_min = 113
target_lon_max = 120
target_lat_min = 17
target_lat_max = 22

fig, ax = plt.subplots(figsize=(10, 8))
plt.rcParams['font.sans-serif'] = ['SimHei']  # 确保使用支持中文的字体
plt.rcParams['font.size'] = 18
m = Basemap(projection='merc', llcrnrlon=lonmin, urcrnrlon=lonmax,
            llcrnrlat=latmin, urcrnrlat=latmax, resolution='i', ax=ax)
m.drawcoastlines(linewidth=1.)

parallels = np.arange(latmin, latmax, 2)
meridians = np.arange(lonmin, lonmax, 2)
m.drawparallels(parallels, labels=[1, 0, 0, 0], dashes=[0, 1])
m.drawmeridians(meridians, labels=[0, 0, 0, 1], dashes=[0, 1])

# 添加地形图
m.etopo()

# 框出指定区域 (东经113-120度，北纬17-22度)
# 获取边界坐标点
x1, y1 = m(target_lon_min, target_lat_min)
x2, y2 = m(target_lon_max, target_lat_min)
x3, y3 = m(target_lon_max, target_lat_max)
x4, y4 = m(target_lon_min, target_lat_max)

# 绘制矩形边框
plt.plot([x1, x2, x3, x4, x1], [y1, y2, y3, y4, y1], 'r-', linewidth=2)

# 添加标题
plt.title('南海区域地图', fontsize=16)

# 添加标记区域的注释
x_text, y_text = m((target_lon_min + target_lon_max) / 2, target_lat_min - 1)
plt.text(x_text, y_text, f'东经{target_lon_min}-{target_lon_max}度，北纬{target_lat_min}-{target_lat_max}度', 
        ha='center', va='top', fontsize=12, color='red')

# 调整布局
plt.tight_layout()

plt.savefig('nanhai_map.png', dpi=300, bbox_inches='tight')
plt.show()