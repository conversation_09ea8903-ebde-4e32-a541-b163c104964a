#!/usr/bin/env python3
"""
SST+PCs+ERA5 ConvLSTM模型测试脚本
验证模型架构和数据加载是否正常

作者: AI Assistant
日期: 2025-07-02
"""

import torch
import numpy as np
from eof_convlstm_model import SSTWithPCsAndERA5ConvLSTMModel, MultiScaleSSTWithPCsAndERA5ConvLSTMModel

print("🧪 测试SST+PCs+ERA5 ConvLSTM模型...")

# 设置设备
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载数据维度信息
print("\n📊 检查数据维度...")
sst_train = np.load('sst_pcs_era5_data/sst_train_norm.npy')
pcs_train = np.load('sst_pcs_era5_data/pcs_train_scaled.npy')
u10_train = np.load('sst_pcs_era5_data/u10_train_norm.npy')

n_time, n_lat, n_lon = sst_train.shape
num_pcs = pcs_train.shape[1]

print(f"SST数据形状: {sst_train.shape}")
print(f"PCs数据形状: {pcs_train.shape}")
print(f"ERA5数据形状: {u10_train.shape}")
print(f"空间维度: {n_lat} x {n_lon}")
print(f"主成分数量: {num_pcs}")

# 模型参数
seq_len = 14
pred_len = 1
batch_size = 2  # 小批次用于测试
hidden_dim = 32  # 减小隐藏维度用于测试

print(f"\n🏗️  模型参数:")
print(f"序列长度: {seq_len}")
print(f"预测长度: {pred_len}")
print(f"批次大小: {batch_size}")
print(f"隐藏维度: {hidden_dim}")

# 创建测试数据
print("\n🔧 创建测试数据...")

# 模拟输入数据: [batch_size, seq_len, 1+num_pcs+4, lat, lon]
input_channels = 1 + num_pcs + 4  # SST + PCs + ERA5(u10,v10,t2m,msl)
test_input = torch.randn(batch_size, seq_len, input_channels, n_lat, n_lon).to(device)
test_target = torch.randn(batch_size, pred_len, n_lat, n_lon).to(device)

print(f"测试输入形状: {test_input.shape}")
print(f"测试目标形状: {test_target.shape}")
print(f"输入通道数: {input_channels} (1 SST + {num_pcs} PCs + 4 ERA5)")

# 测试基础模型
print("\n🧠 测试基础SST+PCs+ERA5 ConvLSTM模型...")

try:
    basic_model = SSTWithPCsAndERA5ConvLSTMModel(
        num_pcs=num_pcs,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=hidden_dim,
        num_layers=2
    ).to(device)
    
    print(f"基础模型参数数量: {sum(p.numel() for p in basic_model.parameters()):,}")
    
    # 前向传播测试
    basic_model.eval()
    with torch.no_grad():
        basic_output = basic_model(test_input)
    
    print(f"✅ 基础模型前向传播成功")
    print(f"输出形状: {basic_output.shape}")
    print(f"输出范围: {basic_output.min().item():.4f} - {basic_output.max().item():.4f}")
    
except Exception as e:
    print(f"❌ 基础模型测试失败: {e}")

# 测试多尺度模型
print("\n🔍 测试多尺度SST+PCs+ERA5 ConvLSTM模型...")

try:
    multiscale_model = MultiScaleSSTWithPCsAndERA5ConvLSTMModel(
        num_pcs=num_pcs,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=hidden_dim,
        num_layers=2
    ).to(device)
    
    print(f"多尺度模型参数数量: {sum(p.numel() for p in multiscale_model.parameters()):,}")
    
    # 前向传播测试
    multiscale_model.eval()
    with torch.no_grad():
        multiscale_output = multiscale_model(test_input)
    
    print(f"✅ 多尺度模型前向传播成功")
    print(f"输出形状: {multiscale_output.shape}")
    print(f"输出范围: {multiscale_output.min().item():.4f} - {multiscale_output.max().item():.4f}")
    
except Exception as e:
    print(f"❌ 多尺度模型测试失败: {e}")

# 测试损失计算
print("\n📏 测试损失计算...")

try:
    criterion = torch.nn.MSELoss()
    
    if 'basic_output' in locals():
        basic_loss = criterion(basic_output, test_target)
        print(f"基础模型损失: {basic_loss.item():.6f}")
    
    if 'multiscale_output' in locals():
        multiscale_loss = criterion(multiscale_output, test_target)
        print(f"多尺度模型损失: {multiscale_loss.item():.6f}")
    
    print("✅ 损失计算正常")
    
except Exception as e:
    print(f"❌ 损失计算失败: {e}")

# 测试实际数据加载
print("\n📂 测试实际数据加载...")

try:
    from train_sst_pcs_era5_convlstm import SSTWithPCsAndERA5Dataset
    
    # 加载实际数据
    sst_train = np.load('sst_pcs_era5_data/sst_train_norm.npy')
    pcs_train = np.load('sst_pcs_era5_data/pcs_train_scaled.npy')
    
    era5_train = {}
    for var in ['u10', 'v10', 't2m', 'msl']:
        era5_train[var] = np.load(f'sst_pcs_era5_data/{var}_train_norm.npy')
    
    # 创建数据集
    dataset = SSTWithPCsAndERA5Dataset(sst_train, pcs_train, era5_train, seq_len, pred_len)
    
    print(f"数据集大小: {len(dataset)}")
    
    # 测试数据加载
    sample_input, sample_target = dataset[0]
    print(f"样本输入形状: {sample_input.shape}")
    print(f"样本目标形状: {sample_target.shape}")
    
    print("✅ 实际数据加载正常")
    
except Exception as e:
    print(f"❌ 实际数据加载失败: {e}")

print("\n🎉 模型测试完成！")

# 内存使用情况
if torch.cuda.is_available():
    print(f"\n💾 GPU内存使用:")
    print(f"已分配: {torch.cuda.memory_allocated()/1024**2:.1f} MB")
    print(f"已缓存: {torch.cuda.memory_reserved()/1024**2:.1f} MB")
