# 海表温度(SST)预测项目代码总结文档

## 📋 项目概述

本项目实现了基于深度学习的海表温度(SST)预测系统，采用多种模型架构和数据融合策略。项目从最初的Transformer+EOF方法发展到ConvLSTM直接预测，并进行了全面的消融实验和改进策略研究。

**核心技术路线:**
- **第一阶段**: Transformer+EOF → PCs预测 → SST重构
- **第二阶段**: ConvLSTM → 直接SST预测 (避免重构误差)
- **第三阶段**: 多源数据融合 (SST+PCs+ERA5)
- **第四阶段**: 消融实验和模型改进

---

## 🧠 核心模型文件

### 1. `eof_convlstm_model.py` - 🌟 **核心模型库**
**功能**: 包含项目中所有深度学习模型架构定义

**主要模型类:**
- `ConvLSTM`: 基础ConvLSTM层实现
- `EOFTransformerModel`: Transformer+EOF模型 (历史方案)
- `EOFConvLSTMModel`: EOF+ConvLSTM模型
- `SSTWithPCsConvLSTMModel`: SST+PCs ConvLSTM模型
- `SSTWithPCsAndERA5ConvLSTMModel`: **主力模型** - SST+PCs+ERA5完整融合
- `MultiScaleSSTWithPCsAndERA5ConvLSTMModel`: 多尺度版本
- `SSTWithERA5ConvLSTMModel`: 消融实验模型 (移除PCs)
- `SSTOnlyConvLSTMModel`: 纯SST基线模型

**技术特点:**
- 多尺度并行ConvLSTM分支 (3x3, 5x5, 7x7卷积核)
- 通道注意力机制和尺度注意力机制
- 残差连接保持时序连续性
- 端到端优化避免重构误差

### 2. `transformer_eof_model.py` - Transformer模型实现
**功能**: 原始Transformer+EOF预测方案

**核心组件:**
- `TransformerEncoder`: 多头注意力编码器
- `EOFTransformerModel`: 完整的Transformer预测模型
- 残差连接和多层结构
- 时间序列预测和EOF重构

---

## 🔧 数据预处理脚本

### 1. `preprocess_and_eof.py` - 🌟 **核心预处理脚本**
**功能**: SST数据预处理和EOF分解

**主要步骤:**
- 读取SST和ERA5原始数据 (`SST-V2.nc`, `data_ERA5.nc`)
- ERA5数据插值到SST网格 (0.25°→0.05°)
- 数据标准化和异常值处理
- EOF分解提取主成分 (PCs) 和空间模态
- 数据集划分 (训练/验证/测试 = 70%/20%/10%)
- 生成可视化图表 (EOF模态、PCs时间序列、方差贡献)

**输出文件:**
- `results/PCs_*.npy`: 主成分时间系数
- `results/EOFs_k.npy`: EOF空间模态
- `results/*_mean.npy`, `*_std.npy`: 标准化参数

### 2. `preprocess_sst_pcs_era5.py` - 多源数据预处理
**功能**: SST+PCs+ERA5数据对齐和标准化

**处理变量:**
- **SST**: 海表温度场
- **PCs**: EOF主成分 (10个)
- **ERA5变量**: u10, v10, t2m, msl (风速、气温、气压)

**关键功能:**
- 时间对齐和空间插值
- 独立标准化各变量
- 数据集划分和保存
- 相关性分析 (t2m与SST相关性0.96)

---

## 🚀 训练脚本

### 1. `train_sst_pcs_era5_convlstm.py` - 🌟 **主要训练脚本**
**功能**: 训练SST+PCs+ERA5完整模型

**训练配置:**
- 序列长度: 14天输入 → 1天预测
- 批次大小: 8 (适应大输入维度)
- 学习率: 0.001, 权重衰减: 1e-4
- 早停机制: 15轮耐心值
- 梯度裁剪: 1.0

**数据集类:**
- `SSTWithPCsAndERA5Dataset`: 多源数据加载器
- 滑动窗口时间序列构建
- 动态数据增强

### 2. `train_sst_era5_convlstm.py` - 消融实验训练
**功能**: 训练SST+ERA5消融模型 (移除PCs特征)

### 3. `train_sst_only_convlstm.py` - 基线模型训练
**功能**: 训练纯SST基线模型

### 4. `train_improved_sst_pcs_era5.py` - 改进模型训练
**功能**: 训练三种改进策略模型
- 选择性PCs使用
- 加权特征融合  
- 分层特征融合

---

## 📊 可视化脚本

### 1. `visualize_sst_pcs_era5_convlstm.py` - 🌟 **主要可视化脚本**
**功能**: 完整模型结果可视化

**可视化内容:**
- SST预测vs真实值对比图
- RMSE空间分布图
- 时间序列误差分析
- 空间平均SST时间序列
- NetCDF格式结果输出

### 2. `visualization_config.py` - 统一可视化配置
**功能**: 定义项目统一的可视化标准

**配置内容:**
- 颜色方案: SST(plasma), 误差(RdBu_r), RMSE(YlOrRd)
- 数值范围: SST(20-32°C), 误差(±2°C), RMSE(0-1°C)
- 图表尺寸和字体配置
- 模型对比配色方案
- 日期映射和格式化函数

---

## 🔬 实验和分析脚本

### 1. `compare_models.py` - 🌟 **模型性能比较框架**
**功能**: 综合比较所有模型性能

**比较指标:**
- RMSE, MAE (摄氏度)
- 空间分布误差
- 时间序列相关性
- PCs预测精度 (针对Transformer模型)

**输出结果:**
- `model_comparison/performance_comparison.csv`: 性能对比表
- `model_comparison/evaluation_report.md`: 综合评估报告
- 各种对比图表 (误差分布、时间序列等)

### 2. `comprehensive_model_comparison.py` - 综合模型对比
**功能**: 深度分析模型性能和特征贡献

**分析内容:**
- 特征贡献度计算
- 相对基线模型的改善程度
- 统计显著性检验
- 模型复杂度分析

### 3. `convert_to_celsius_metrics.py` - 摄氏度指标转换
**功能**: 将模型输出转换为摄氏度并重新评估

### 4. `evaluate_improved_models.py` - 改进模型评估
**功能**: 详细评估三种改进策略的效果

---

## 🧪 测试脚本

### 1. `test_sst_pcs_era5_model.py` - 主模型测试
**功能**: 验证SST+PCs+ERA5模型架构

**测试内容:**
- 模型前向传播
- 参数数量统计
- 数据加载器验证
- 输出形状检查

### 2. `test_ablation_models.py` - 消融模型测试
**功能**: 验证SST+ERA5消融模型

### 3. `test_sst_only_model.py` - 基线模型测试
**功能**: 验证纯SST基线模型

---

## 🔄 自动化流水线脚本

### 1. `run_sst_pcs_era5_pipeline.py` - 🌟 **完整流水线**
**功能**: 一键运行完整实验流程

**执行步骤:**
1. 数据预处理 (`preprocess_sst_pcs_era5.py`)
2. 模型训练 (`train_sst_pcs_era5_convlstm.py`)
3. 结果可视化 (`visualize_sst_pcs_era5_convlstm.py`)
4. 模型比较 (`compare_models.py`)

**命令行参数:**
- `--model-type`: basic/multiscale/both
- `--skip-training`: 跳过训练
- `--skip-preprocessing`: 跳过预处理

### 2. `run_ablation_study.py` - 消融实验流水线
**功能**: 自动化消融实验流程

**实验目标**: 量化PCs特征对SST预测性能的贡献

### 3. `run_eof_convlstm_pipeline.py` - EOF+ConvLSTM流水线
**功能**: 运行EOF-ConvLSTM实验 (历史方案)

---

## 🔧 改进策略实现

### 1. `improve_sst_pcs_era5_model.py` - 模型改进策略
**功能**: 实现三种模型改进策略

**改进策略:**
1. **选择性PCs**: 只使用前5个最重要的主成分
2. **加权融合**: PCs和ERA5特征加权融合
3. **分层融合**: 分层处理不同类型特征

**模型类:**
- `ImprovedSSTWithPCsAndERA5ConvLSTMModel`
- 支持策略选择的统一接口
- 自适应特征融合机制

---

## 📁 重要数据文件夹

### 输入数据
- `SST-V2.nc`: 海表温度原始数据
- `data_ERA5.nc`: ERA5大气再分析数据

### 预处理数据
- `results/`: EOF分解结果和PCs数据
- `sst_pcs_era5_data/`: 多源融合预处理数据

### 模型结果
- `sst_pcs_era5_results/`: 完整模型训练结果
- `sst_era5_ablation_results/`: 消融实验结果
- `improved_sst_pcs_era5_results/`: 改进模型结果

### 可视化结果
- `sst_pcs_era5_visualization/`: 完整模型可视化
- `model_comparison/`: 模型性能比较图表

---

## 🎯 核心技术亮点

### 1. 多源数据融合
- **海洋信息**: SST场提供海洋状态
- **降维信息**: PCs提供主要模态特征
- **大气信息**: ERA5提供大气强迫

### 2. 端到端优化
- 直接预测SST场，避免PCs→SST重构误差
- 残差连接保持时序连续性
- 多尺度特征提取

### 3. 物理机制考虑
- 大气-海洋相互作用
- 风应力对SST的影响 (u10, v10)
- 气温与SST的热交换 (t2m)
- 气压系统影响 (msl)

### 4. 实验设计完整性
- 消融实验量化特征贡献
- 多种改进策略对比
- 统一的评估和可视化框架

---

## 📈 性能基准

**最佳模型性能** (SST+PCs+ERA5):
- **RMSE**: ~0.26°C
- **MAE**: ~0.20°C

**消融实验发现**:
- 移除PCs后性能略有提升，说明ERA5特征更重要
- 纯SST基线模型性能最差
- 改进策略中选择性PCs效果最佳

---

## 🚀 快速使用指南

### 完整流水线 (推荐)
```bash
# 运行完整实验
python run_sst_pcs_era5_pipeline.py --model-type both

# 运行消融实验
python run_ablation_study.py --model-type basic
```

### 分步执行
```bash
# 1. 数据预处理
python preprocess_sst_pcs_era5.py

# 2. 训练模型
python train_sst_pcs_era5_convlstm.py

# 3. 可视化结果
python visualize_sst_pcs_era5_convlstm.py

# 4. 模型比较
python compare_models.py
```

---

## 📚 文档说明

- `README.md`: 项目基础说明
- `PROJECT_OVERVIEW.md`: 项目总体概述
- `PROJECT_FILE_STRUCTURE.md`: 详细文件结构说明
- `SST_PCs_ERA5_ConvLSTM_README.md`: 主要技术方案文档
- `Ablation_Study_README.md`: 消融实验说明
- `VISUALIZATION_STANDARDIZATION_SUMMARY.md`: 可视化标准化总结

本项目实现了从数据预处理到模型训练、评估、可视化的完整深度学习流水线，为海表温度预测提供了多种先进的技术方案。
