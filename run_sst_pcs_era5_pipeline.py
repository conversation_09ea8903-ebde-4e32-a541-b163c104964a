#!/usr/bin/env python3
"""
SST+PCs+ERA5 ConvLSTM完整流水线脚本
自动化执行数据预处理、模型训练、结果可视化和性能比较

作者: AI Assistant
日期: 2025-07-02
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n{'='*60}")
    print(f"开始执行: {description}")
    print(f"命令: {command}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {description} 完成！用时: {duration:.2f}秒")
        
        # 打印输出的最后几行
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            print("输出摘要:")
            for line in lines[-5:]:  # 显示最后5行
                print(f"  {line}")
                
        return True
        
    except subprocess.CalledProcessError as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"❌ {description} 失败！用时: {duration:.2f}秒")
        print(f"错误代码: {e.returncode}")
        print(f"错误信息: {e.stderr}")
        
        return False

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    required_files = [
        'SST-V2.nc',
        'data_ERA5.nc',
        'results/PCs_train.npy',
        'results/PCs_val.npy',
        'results/PCs_test.npy'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少以下必需文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        
        if any('PCs_' in f for f in missing_files):
            print("\n💡 请先运行 EOF 分解:")
            print("   python preprocess_and_eof.py")
        
        return False
    
    print("✅ 所有前置条件满足")
    return True

def main():
    parser = argparse.ArgumentParser(description='SST+PCs+ERA5 ConvLSTM完整流水线')
    parser.add_argument('--model-type', choices=['basic', 'multiscale', 'both'], 
                       default='both', help='要训练的模型类型')
    parser.add_argument('--skip-preprocessing', action='store_true', 
                       help='跳过数据预处理步骤')
    parser.add_argument('--skip-training', action='store_true', 
                       help='跳过模型训练步骤')
    parser.add_argument('--skip-visualization', action='store_true', 
                       help='跳过结果可视化步骤')
    parser.add_argument('--skip-comparison', action='store_true', 
                       help='跳过模型比较步骤')
    
    args = parser.parse_args()
    
    print("🚀 SST+PCs+ERA5 ConvLSTM 完整流水线")
    print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 模型类型: {args.model_type}")
    
    # 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件不满足，退出流水线")
        return 1
    
    success_count = 0
    total_steps = 0
    
    # 步骤1: 数据预处理
    if not args.skip_preprocessing:
        total_steps += 1
        print(f"\n📊 步骤 {total_steps}: 数据预处理")
        if run_command("python preprocess_sst_pcs_era5.py", "SST+PCs+ERA5数据预处理"):
            success_count += 1
        else:
            print("❌ 数据预处理失败，退出流水线")
            return 1
    else:
        print("\n⏭️  跳过数据预处理步骤")
    
    # 步骤2: 模型训练
    if not args.skip_training:
        total_steps += 1
        print(f"\n🧠 步骤 {total_steps}: 模型训练")
        
        # 修改训练脚本中的模型类型
        train_command = f"python -c \"exec(open('train_sst_pcs_era5_convlstm.py').read().replace('model_type = \\\"basic\\\"', 'model_type = \\\"{args.model_type}\\\"))\""
        
        if run_command(train_command, f"训练{args.model_type}模型"):
            success_count += 1
        else:
            print("❌ 模型训练失败，但继续执行后续步骤")
    else:
        print("\n⏭️  跳过模型训练步骤")
    
    # 步骤3: 结果可视化
    if not args.skip_visualization:
        total_steps += 1
        print(f"\n📈 步骤 {total_steps}: 结果可视化")
        if run_command("python visualize_sst_pcs_era5_convlstm.py", "结果可视化"):
            success_count += 1
        else:
            print("❌ 结果可视化失败，但继续执行后续步骤")
    else:
        print("\n⏭️  跳过结果可视化步骤")
    
    # 步骤4: 模型比较
    if not args.skip_comparison:
        total_steps += 1
        print(f"\n🔍 步骤 {total_steps}: 模型性能比较")
        if run_command("python compare_models.py", "模型性能比较"):
            success_count += 1
        else:
            print("❌ 模型比较失败")
    else:
        print("\n⏭️  跳过模型比较步骤")
    
    # 总结
    print(f"\n{'='*60}")
    print("🎉 流水线执行完成！")
    print(f"📅 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"✅ 成功步骤: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("🎊 所有步骤都成功完成！")
        
        print("\n📁 输出文件位置:")
        print("  - 预处理数据: sst_pcs_era5_data/")
        print("  - 训练结果: sst_pcs_era5_results/")
        print("  - 训练图表: sst_pcs_era5_figures/")
        print("  - 可视化结果: sst_pcs_era5_visualization/")
        print("  - 模型比较: model_comparison/")
        
        print("\n🔍 主要结果文件:")
        print("  - 最佳模型权重: sst_pcs_era5_results/best_sst_pcs_era5_*.pth")
        print("  - 预测结果: sst_pcs_era5_results/test_predictions_*.npy")
        print("  - NetCDF结果: sst_pcs_era5_visualization/sst_prediction_results_*.nc")
        print("  - 性能比较: model_comparison/performance_comparison.csv")
        print("  - 评估报告: model_comparison/evaluation_report.md")
        
        return 0
    else:
        print(f"⚠️  有 {total_steps - success_count} 个步骤失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
