#!/usr/bin/env python3
"""
EOF加权空间映射ConvLSTM模型
实现策略2：使用EOF模态的空间结构对PCs进行加权映射
将加权场作为额外输入通道与SST和ERA5数据结合

作者: AI Assistant
日期: 2025-07-07
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class ConvLSTMCell(nn.Module):
    """ConvLSTM单元"""
    
    def __init__(self, input_dim, hidden_dim, kernel_size, bias):
        super(ConvLSTMCell, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.padding = kernel_size[0] // 2, kernel_size[1] // 2
        self.bias = bias
        
        self.conv = nn.Conv2d(in_channels=self.input_dim + self.hidden_dim,
                              out_channels=4 * self.hidden_dim,
                              kernel_size=self.kernel_size,
                              padding=self.padding,
                              bias=self.bias)

    def forward(self, input_tensor, cur_state):
        h_cur, c_cur = cur_state
        
        combined = torch.cat([input_tensor, h_cur], dim=1)
        combined_conv = self.conv(combined)
        
        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_dim, dim=1)
        i = torch.sigmoid(cc_i)
        f = torch.sigmoid(cc_f)
        o = torch.sigmoid(cc_o)
        g = torch.tanh(cc_g)

        c_next = f * c_cur + i * g
        h_next = o * torch.tanh(c_next)
        
        return h_next, c_next

    def init_hidden(self, batch_size, image_size):
        height, width = image_size
        return (torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device),
                torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device))


class ConvLSTM(nn.Module):
    """ConvLSTM网络"""
    
    def __init__(self, input_dim, hidden_dim, kernel_size, num_layers,
                 batch_first=False, bias=True, return_all_layers=False):
        super(ConvLSTM, self).__init__()

        self._check_kernel_size_consistency(kernel_size)

        kernel_size = self._extend_for_multilayer(kernel_size, num_layers)
        hidden_dim = self._extend_for_multilayer(hidden_dim, num_layers)
        if not len(kernel_size) == len(hidden_dim) == num_layers:
            raise ValueError('Inconsistent list length.')

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.num_layers = num_layers
        self.batch_first = batch_first
        self.bias = bias
        self.return_all_layers = return_all_layers

        cell_list = []
        for i in range(0, self.num_layers):
            cur_input_dim = self.input_dim if i == 0 else self.hidden_dim[i - 1]
            cell_list.append(ConvLSTMCell(input_dim=cur_input_dim,
                                          hidden_dim=self.hidden_dim[i],
                                          kernel_size=self.kernel_size[i],
                                          bias=self.bias))

        self.cell_list = nn.ModuleList(cell_list)

    def forward(self, input_tensor, hidden_state=None):
        if not self.batch_first:
            input_tensor = input_tensor.permute(1, 0, 2, 3, 4)

        b, _, _, h, w = input_tensor.size()

        if hidden_state is not None:
            raise NotImplementedError()
        else:
            hidden_state = self._init_hidden(batch_size=b, image_size=(h, w))

        layer_output_list = []
        last_state_list = []

        seq_len = input_tensor.size(1)
        cur_layer_input = input_tensor

        for layer_idx in range(self.num_layers):
            h, c = hidden_state[layer_idx]
            output_inner = []
            for t in range(seq_len):
                h, c = self.cell_list[layer_idx](input_tensor=cur_layer_input[:, t, :, :, :],
                                                 cur_state=[h, c])
                output_inner.append(h)

            layer_output = torch.stack(output_inner, dim=1)
            cur_layer_input = layer_output

            layer_output_list.append(layer_output)
            last_state_list.append([h, c])

        if not self.return_all_layers:
            layer_output_list = layer_output_list[-1:]
            last_state_list = last_state_list[-1:]

        return layer_output_list, last_state_list

    def _init_hidden(self, batch_size, image_size):
        init_states = []
        for i in range(self.num_layers):
            init_states.append(self.cell_list[i].init_hidden(batch_size, image_size))
        return init_states

    @staticmethod
    def _check_kernel_size_consistency(kernel_size):
        if not (isinstance(kernel_size, tuple) or
                (isinstance(kernel_size, list) and all([isinstance(elem, tuple) for elem in kernel_size]))):
            raise ValueError('`kernel_size` must be tuple or list of tuples')

    @staticmethod
    def _extend_for_multilayer(param, num_layers):
        if not isinstance(param, list):
            param = [param] * num_layers
        return param


class EOFWeightedConvLSTMModel(nn.Module):
    """
    EOF加权空间映射ConvLSTM模型
    
    输入特征:
    1. SST历史序列: [batch_size, seq_len, 1, lat, lon]
    2. ERA5大气变量: [batch_size, seq_len, 4, lat, lon] (u10, v10, t2m, msl)
    3. EOF加权特征: [batch_size, seq_len, k, lat, lon] (k个EOF模态的加权空间场)
    
    总输入: [batch_size, seq_len, 1+4+k, lat, lon]
    输出: 预测的SST场 [batch_size, pred_len, lat, lon]
    """
    
    def __init__(self, num_eof_modes=10, seq_len=14, pred_len=1, hidden_dim=64,
                 kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(EOFWeightedConvLSTMModel, self).__init__()
        
        self.num_eof_modes = num_eof_modes
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dim = hidden_dim
        
        # 输入通道数: 1(SST) + 4(ERA5) + k(EOF加权特征)
        self.input_dim = 1 + 4 + num_eof_modes
        
        print(f"EOF加权ConvLSTM模型初始化:")
        print(f"  输入通道数: {self.input_dim} (1 SST + 4 ERA5 + {num_eof_modes} EOF)")
        print(f"  隐藏维度: {hidden_dim}")
        print(f"  序列长度: {seq_len}")
        
        # EOF特征预处理层
        self.eof_feature_processor = nn.Sequential(
            nn.Conv2d(num_eof_modes, num_eof_modes, kernel_size=3, padding=1, groups=num_eof_modes),
            nn.BatchNorm2d(num_eof_modes),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout * 0.5)
        )
        
        # 多尺度特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(self.input_dim, self.input_dim, kernel_size=1, padding=0),
            nn.BatchNorm2d(self.input_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )
        
        # ConvLSTM核心层
        self.convlstm = ConvLSTM(
            input_dim=self.input_dim,
            hidden_dim=hidden_dim,
            kernel_size=kernel_size,
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # 注意力机制 - 用于融合不同类型的特征
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(self.input_dim, self.input_dim // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.input_dim // 4, self.input_dim, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim // 2, hidden_dim // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 4, 1, kernel_size=1, padding=0)
        )
        
        # 残差连接
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1, padding=0)
        
    def forward(self, sst_sequence, era5_sequence, eof_weighted_sequence):
        """
        前向传播
        
        参数:
        - sst_sequence: [batch_size, seq_len, 1, lat, lon]
        - era5_sequence: [batch_size, seq_len, 4, lat, lon]
        - eof_weighted_sequence: [batch_size, seq_len, k, lat, lon]
        
        返回:
        - output_sst: [batch_size, pred_len, lat, lon]
        """
        batch_size, seq_len, _, lat, lon = sst_sequence.size()
        
        # 处理每个时间步的特征
        processed_sequence = []
        
        for t in range(seq_len):
            # 获取当前时间步的所有特征
            sst_t = sst_sequence[:, t]          # [batch_size, 1, lat, lon]
            era5_t = era5_sequence[:, t]        # [batch_size, 4, lat, lon]
            eof_t = eof_weighted_sequence[:, t] # [batch_size, k, lat, lon]
            
            # 处理EOF特征
            eof_processed = self.eof_feature_processor(eof_t)
            
            # 拼接所有特征
            combined_features = torch.cat([sst_t, era5_t, eof_processed], dim=1)
            # [batch_size, 1+4+k, lat, lon]
            
            # 通道注意力
            attention_weights = self.channel_attention(combined_features)
            attended_features = combined_features * attention_weights
            
            # 特征融合
            fused_features = self.feature_fusion(attended_features)
            
            processed_sequence.append(fused_features)
        
        # 重新组合时间序列
        processed_input = torch.stack(processed_sequence, dim=1)
        # [batch_size, seq_len, input_dim, lat, lon]
        
        # ConvLSTM前向传播
        layer_output_list, last_state_list = self.convlstm(processed_input)
        convlstm_output = layer_output_list[0]  # [batch_size, seq_len, hidden_dim, lat, lon]
        
        # 使用最后一个时间步进行预测
        last_output = convlstm_output[:, -1]  # [batch_size, hidden_dim, lat, lon]
        
        # 输出投影
        predicted_sst = self.output_projection(last_output)  # [batch_size, 1, lat, lon]
        
        # 残差连接 - 与输入的最后一个时间步的SST相加
        last_sst = sst_sequence[:, -1, 0:1, :, :]  # [batch_size, 1, lat, lon]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst
        
        # 调整输出形状
        output_sst = output_sst.squeeze(1)  # [batch_size, lat, lon]
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)  # [batch_size, 1, lat, lon]
        
        return output_sst


if __name__ == "__main__":
    # 测试模型
    print("🧪 测试EOF加权ConvLSTM模型...")

    # 模拟输入数据
    batch_size = 2
    seq_len = 14
    pred_len = 1
    lat, lon = 100, 140
    num_eof_modes = 10

    # 创建模拟数据
    sst_input = torch.randn(batch_size, seq_len, 1, lat, lon)
    era5_input = torch.randn(batch_size, seq_len, 4, lat, lon)
    eof_weighted_input = torch.randn(batch_size, seq_len, num_eof_modes, lat, lon)

    # 测试基础模型
    model = EOFWeightedConvLSTMModel(
        num_eof_modes=num_eof_modes,
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=32  # 减小用于测试
    )

    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 前向传播测试
    with torch.no_grad():
        output = model(sst_input, era5_input, eof_weighted_input)
        print(f"输出形状: {output.shape}")
        print(f"输出范围: {output.min().item():.4f} - {output.max().item():.4f}")

    print("✅ EOF加权ConvLSTM模型测试通过！")


class MultiScaleEOFWeightedConvLSTMModel(nn.Module):
    """
    多尺度EOF加权ConvLSTM模型
    使用不同尺度的卷积核处理不同的特征类型
    """
    
    def __init__(self, num_eof_modes=10, seq_len=14, pred_len=1, hidden_dim=64,
                 kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(MultiScaleEOFWeightedConvLSTMModel, self).__init__()
        
        self.num_eof_modes = num_eof_modes
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dim = hidden_dim
        
        # 输入通道数
        self.input_dim = 1 + 4 + num_eof_modes
        
        # 多尺度ConvLSTM分支
        # 细尺度分支 - 处理局地特征
        self.convlstm_fine = ConvLSTM(
            input_dim=self.input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(3, 3),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # 中尺度分支 - 处理区域特征
        self.convlstm_medium = ConvLSTM(
            input_dim=self.input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(5, 5),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # 粗尺度分支 - 处理大尺度特征
        self.convlstm_coarse = ConvLSTM(
            input_dim=self.input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(7, 7),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(self.input_dim, self.input_dim, kernel_size=1, padding=0),
            nn.BatchNorm2d(self.input_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )
        
        # 多尺度特征融合
        fusion_dim = hidden_dim * 3
        self.multiscale_fusion = nn.Sequential(
            nn.Conv2d(fusion_dim, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )
        
        # 输出投影
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 2, 1, kernel_size=1, padding=0)
        )
        
        # 残差连接
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1, padding=0)
        
    def forward(self, sst_sequence, era5_sequence, eof_weighted_sequence):
        batch_size, seq_len, _, lat, lon = sst_sequence.size()
        
        # 组合所有输入特征
        combined_input = torch.cat([sst_sequence, era5_sequence, eof_weighted_sequence], dim=2)
        
        # 特征融合
        processed_sequence = []
        for t in range(seq_len):
            fused_t = self.feature_fusion(combined_input[:, t])
            processed_sequence.append(fused_t)
        
        processed_input = torch.stack(processed_sequence, dim=1)
        
        # 多尺度ConvLSTM处理
        fine_output, _ = self.convlstm_fine(processed_input)
        medium_output, _ = self.convlstm_medium(processed_input)
        coarse_output, _ = self.convlstm_coarse(processed_input)
        
        # 获取最后时间步输出
        fine_last = fine_output[0][:, -1]
        medium_last = medium_output[0][:, -1]
        coarse_last = coarse_output[0][:, -1]
        
        # 多尺度特征融合
        multiscale_features = torch.cat([fine_last, medium_last, coarse_last], dim=1)
        fused_features = self.multiscale_fusion(multiscale_features)
        
        # 输出投影
        predicted_sst = self.output_projection(fused_features)
        
        # 残差连接
        last_sst = sst_sequence[:, -1, 0:1, :, :]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst
        
        # 调整输出形状
        output_sst = output_sst.squeeze(1)
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)
        
        return output_sst
