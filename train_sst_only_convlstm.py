#!/usr/bin/env python3
"""
纯SST ConvLSTM模型训练脚本
仅使用SST历史序列作为输入，直接预测未来SST
作为最简单的基线模型，用于验证其他特征的贡献

作者: AI Assistant
日期: 2025-07-02
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import os
import time
from sklearn.metrics import mean_squared_error, mean_absolute_error
from tqdm import tqdm

# 导入纯SST模型
from eof_convlstm_model import SSTOnlyConvLSTMModel

print("开始纯SST ConvLSTM模型训练...")

# 创建结果文件夹
os.makedirs('sst_only_results', exist_ok=True)
os.makedirs('sst_only_figures', exist_ok=True)

# 设置随机种子确保可重复性
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载预处理后的SST数据
print("加载预处理后的SST数据...")

# 只需要SST数据
sst_train = np.load('sst_pcs_era5_data/sst_train_norm.npy')
sst_val = np.load('sst_pcs_era5_data/sst_val_norm.npy')
sst_test = np.load('sst_pcs_era5_data/sst_test_norm.npy')

print(f"SST训练集形状: {sst_train.shape}")
print(f"SST验证集形状: {sst_val.shape}")
print(f"SST测试集形状: {sst_test.shape}")

# 获取数据维度
n_time_train, n_lat, n_lon = sst_train.shape

print(f"数据维度: 时间={n_time_train}, 纬度={n_lat}, 经度={n_lon}")
print("注意: 纯SST模型只使用SST历史序列作为输入")

class SSTOnlyDataset(Dataset):
    """纯SST数据集类 (只使用SST)"""
    
    def __init__(self, sst_data, seq_len=14, pred_len=1):
        self.sst_data = sst_data
        self.seq_len = seq_len
        self.pred_len = pred_len
        
        # 计算有效样本数量
        self.n_samples = len(sst_data) - seq_len - pred_len + 1
        
    def __len__(self):
        return self.n_samples
    
    def __getitem__(self, idx):
        # 输入序列: 只有SST
        input_sst = self.sst_data[idx:idx+self.seq_len]  # [seq_len, lat, lon]
        
        # 组合输入特征: [seq_len, 1, lat, lon] (只有SST)
        combined_input = input_sst[:, np.newaxis, :, :]  # 添加通道维度
        
        # 目标SST
        target_sst = self.sst_data[idx+self.seq_len:idx+self.seq_len+self.pred_len]
        
        return torch.FloatTensor(combined_input), torch.FloatTensor(target_sst)

# 超参数设置
seq_len = 14
pred_len = 1
batch_size = 8  # 保持一致
hidden_dim = 64
num_layers = 2
learning_rate = 0.001
weight_decay = 1e-4
num_epochs = 80
patience = 15
clip_grad = 1.0

print(f"超参数设置:")
print(f"  序列长度: {seq_len}")
print(f"  预测长度: {pred_len}")
print(f"  批次大小: {batch_size}")
print(f"  隐藏维度: {hidden_dim}")
print(f"  学习率: {learning_rate}")
print(f"  输入通道数: 1 (仅SST)")

# 创建数据集和数据加载器
print("创建数据集和数据加载器...")

train_dataset = SSTOnlyDataset(sst_train, seq_len, pred_len)
val_dataset = SSTOnlyDataset(sst_val, seq_len, pred_len)
test_dataset = SSTOnlyDataset(sst_test, seq_len, pred_len)

train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

print(f"数据集大小:")
print(f"  训练集: {len(train_dataset)} 样本")
print(f"  验证集: {len(val_dataset)} 样本")
print(f"  测试集: {len(test_dataset)} 样本")

# 训练函数
def train_model(model, model_name):
    print(f"\n开始训练 {model_name} 纯SST模型...")
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    criterion = nn.MSELoss()
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    print(f"开始训练，共 {num_epochs} 个epoch...")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        
        for batch_idx, (inputs, targets) in enumerate(train_pbar):
            inputs, targets = inputs.to(device), targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)
            
            optimizer.step()
            train_loss += loss.item()
            
            train_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for inputs, targets in val_pbar:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
                val_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_val_loss = val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  训练损失: {avg_train_loss:.6f}')
        print(f'  验证损失: {avg_val_loss:.6f}')
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), f'sst_only_results/best_{model_name}.pth')
            print(f'  保存最佳模型 (验证损失: {best_val_loss:.6f})')
        else:
            patience_counter += 1
            print(f'  早停计数: {patience_counter}/{patience}')
        
        if patience_counter >= patience:
            print(f'早停触发，停止训练')
            break
    
    return train_losses, val_losses

# 测试模型性能
def test_model(model, model_name):
    print(f"\n测试 {model_name} 纯SST模型性能...")
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'sst_only_results/best_{model_name}.pth'))
    model.eval()
    
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc=f'测试 {model_name}')
        for inputs, targets in test_pbar:
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            
            test_predictions.append(outputs.cpu().numpy())
            test_targets.append(targets.cpu().numpy())
    
    # 合并所有预测结果
    test_predictions = np.concatenate(test_predictions, axis=0)
    test_targets = np.concatenate(test_targets, axis=0)
    
    # 计算评估指标
    mse = mean_squared_error(test_targets.flatten(), test_predictions.flatten())
    mae = mean_absolute_error(test_targets.flatten(), test_predictions.flatten())
    rmse = np.sqrt(mse)
    
    print(f"{model_name} 纯SST模型测试结果:")
    print(f"  MSE: {mse:.6f}")
    print(f"  MAE: {mae:.6f}")
    print(f"  RMSE: {rmse:.6f}")
    
    # 保存测试结果
    np.save(f'sst_only_results/test_predictions_{model_name}.npy', test_predictions)
    np.save(f'sst_only_results/test_targets_{model_name}.npy', test_targets)
    
    return test_predictions, test_targets, {'mse': mse, 'mae': mae, 'rmse': rmse}

# 绘制训练曲线
def plot_training_curves(train_losses, val_losses, model_name):
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='训练损失', color='blue')
    plt.plot(val_losses, label='验证损失', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.title(f'{model_name} 纯SST模型训练曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'sst_only_figures/training_curves_{model_name}.png', dpi=300, bbox_inches='tight')
    plt.close()

# 训练纯SST模型
print("\n" + "="*60)
print("训练纯SST ConvLSTM模型")
print("="*60)

# 创建纯SST模型
sst_only_model = SSTOnlyConvLSTMModel(
    seq_len=seq_len,
    pred_len=pred_len,
    hidden_dim=hidden_dim,
    num_layers=num_layers
).to(device)

print(f"纯SST模型参数数量: {sum(p.numel() for p in sst_only_model.parameters()):,}")

# 训练模型
train_losses, val_losses = train_model(sst_only_model, "sst_only_basic")

# 测试模型
test_predictions, test_targets, test_metrics = test_model(sst_only_model, "sst_only_basic")
plot_training_curves(train_losses, val_losses, "sst_only_basic")

print("\n纯SST ConvLSTM模型训练完成！")
print("这是最简单的基线模型，用于验证其他特征的贡献")
