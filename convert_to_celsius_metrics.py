#!/usr/bin/env python3
"""
将改进模型结果转换为摄氏度并计算实际评估指标
提供更直观的性能评估

作者: AI Assistant
日期: 2025-07-02
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False
import os
from sklearn.metrics import mean_squared_error, mean_absolute_error

print("🌡️  转换改进模型结果为摄氏度评估指标...")

# 创建结果文件夹
os.makedirs('celsius_evaluation_results', exist_ok=True)

# 加载标准化参数
sst_mean = np.load('sst_pcs_era5_data/sst_mean.npy')
sst_std = np.load('sst_pcs_era5_data/sst_std.npy')

print(f"SST标准化参数: mean={sst_mean:.3f}K, std={sst_std:.3f}K")

def denormalize_and_convert_to_celsius(sst_norm):
    """反标准化并转换为摄氏度"""
    # 反标准化到开尔文
    sst_kelvin = sst_norm * sst_std + sst_mean
    # 转换为摄氏度
    sst_celsius = sst_kelvin - 273.15
    return sst_celsius

# 定义所有模型
models_to_evaluate = {
    'original_full': {
        'name': '原始完整模型 (SST+PCs+ERA5)',
        'pred_file': 'sst_pcs_era5_results/test_predictions_sst_pcs_era5_basic.npy',
        'target_file': 'sst_pcs_era5_results/test_targets_sst_pcs_era5_basic.npy',
        'description': '基准模型，使用所有15个输入通道'
    },
    'ablation': {
        'name': '消融模型 (SST+ERA5)',
        'pred_file': 'sst_era5_ablation_results/test_predictions_sst_era5_ablation_basic.npy',
        'target_file': 'sst_era5_ablation_results/test_targets_sst_era5_ablation_basic.npy',
        'description': '移除PCs特征，只使用5个输入通道'
    },
    'improved_selective_pcs': {
        'name': '改进模型-选择性PCs',
        'pred_file': 'improved_sst_pcs_era5_results/test_predictions_improved_selective_pcs.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_selective_pcs.npy',
        'description': '只使用前5个最重要的PCs'
    },
    'improved_weighted_fusion': {
        'name': '改进模型-加权融合',
        'pred_file': 'improved_sst_pcs_era5_results/test_predictions_improved_weighted_fusion.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_weighted_fusion.npy',
        'description': '对不同特征组应用不同权重'
    },
    'improved_hierarchical_fusion': {
        'name': '改进模型-分层融合',
        'pred_file': 'improved_sst_pcs_era5_results/test_predictions_improved_hierarchical_fusion.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_hierarchical_fusion.npy',
        'description': '分层处理不同类型特征'
    }
}

# 计算所有模型的摄氏度评估指标
print("\n计算摄氏度评估指标...")
celsius_results = {}

for model_key, model_info in models_to_evaluate.items():
    try:
        # 加载预测结果
        predictions_norm = np.load(model_info['pred_file'])
        targets_norm = np.load(model_info['target_file'])
        
        # 转换为摄氏度
        predictions_celsius = denormalize_and_convert_to_celsius(predictions_norm)
        targets_celsius = denormalize_and_convert_to_celsius(targets_norm)
        
        # 计算评估指标
        rmse_celsius = np.sqrt(mean_squared_error(targets_celsius.flatten(), predictions_celsius.flatten()))
        mae_celsius = mean_absolute_error(targets_celsius.flatten(), predictions_celsius.flatten())
        
        # 计算其他统计指标
        error_celsius = predictions_celsius - targets_celsius
        bias_celsius = np.mean(error_celsius)
        std_error_celsius = np.std(error_celsius)
        max_error_celsius = np.max(np.abs(error_celsius))
        min_error_celsius = np.min(error_celsius)
        
        # 计算相关系数
        correlation = np.corrcoef(predictions_celsius.flatten(), targets_celsius.flatten())[0, 1]
        
        # 存储结果
        celsius_results[model_key] = {
            'name': model_info['name'],
            'description': model_info['description'],
            'rmse': rmse_celsius,
            'mae': mae_celsius,
            'bias': bias_celsius,
            'std_error': std_error_celsius,
            'max_error': max_error_celsius,
            'min_error': min_error_celsius,
            'correlation': correlation,
            'pred_range': (predictions_celsius.min(), predictions_celsius.max()),
            'true_range': (targets_celsius.min(), targets_celsius.max())
        }
        
        print(f"✅ {model_info['name']}: RMSE={rmse_celsius:.4f}°C, MAE={mae_celsius:.4f}°C")
        
    except FileNotFoundError:
        print(f"⚠️  未找到 {model_info['name']} 的结果文件")
        continue

if not celsius_results:
    print("❌ 未找到任何模型结果")
    exit(1)

# 创建详细的摄氏度评估报告
print("\n生成详细评估报告...")

# 1. 创建性能对比表
comparison_data = []
for model_key, results in celsius_results.items():
    comparison_data.append({
        '模型名称': results['name'],
        '模型描述': results['description'],
        'RMSE (°C)': f"{results['rmse']:.4f}",
        'MAE (°C)': f"{results['mae']:.4f}",
        '偏差 (°C)': f"{results['bias']:.4f}",
        '误差标准差 (°C)': f"{results['std_error']:.4f}",
        '最大误差 (°C)': f"{results['max_error']:.4f}",
        '最小误差 (°C)': f"{results['min_error']:.4f}",
        '相关系数': f"{results['correlation']:.4f}",
        '预测范围 (°C)': f"{results['pred_range'][0]:.2f} - {results['pred_range'][1]:.2f}",
        '真实范围 (°C)': f"{results['true_range'][0]:.2f} - {results['true_range'][1]:.2f}"
    })

df_celsius = pd.DataFrame(comparison_data)
df_celsius.to_csv('celsius_evaluation_results/celsius_performance_comparison.csv', 
                 index=False, encoding='utf-8-sig')

# 2. 计算相对于基准模型的改善
if 'original_full' in celsius_results:
    baseline_rmse = celsius_results['original_full']['rmse']
    baseline_mae = celsius_results['original_full']['mae']
    
    print(f"\n📊 相对于原始完整模型的改善:")
    print(f"基准模型 - RMSE: {baseline_rmse:.4f}°C, MAE: {baseline_mae:.4f}°C")
    print("-" * 60)
    
    improvement_data = []
    for model_key, results in celsius_results.items():
        if model_key != 'original_full':
            rmse_improvement = ((baseline_rmse - results['rmse']) / baseline_rmse) * 100
            mae_improvement = ((baseline_mae - results['mae']) / baseline_mae) * 100
            
            improvement_data.append({
                '模型': results['name'],
                'RMSE改善 (%)': f"{rmse_improvement:.2f}",
                'MAE改善 (%)': f"{mae_improvement:.2f}",
                'RMSE (°C)': f"{results['rmse']:.4f}",
                'MAE (°C)': f"{results['mae']:.4f}"
            })
            
            print(f"{results['name']}:")
            print(f"  RMSE: {results['rmse']:.4f}°C ({rmse_improvement:+.2f}%)")
            print(f"  MAE: {results['mae']:.4f}°C ({mae_improvement:+.2f}%)")
    
    df_improvement = pd.DataFrame(improvement_data)
    df_improvement.to_csv('celsius_evaluation_results/improvement_analysis.csv', 
                         index=False, encoding='utf-8-sig')

# 3. 绘制摄氏度性能对比图
print("\n绘制摄氏度性能对比图...")

fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

# RMSE对比
model_names = [results['name'].replace('改进模型-', '') for results in celsius_results.values()]
rmse_values = [results['rmse'] for results in celsius_results.values()]
colors = ['blue', 'red', 'green', 'orange', 'purple'][:len(model_names)]

bars1 = ax1.bar(range(len(model_names)), rmse_values, color=colors, alpha=0.7)
ax1.set_xlabel('模型')
ax1.set_ylabel('RMSE (°C)')
ax1.set_title('模型RMSE对比 (摄氏度)')
ax1.set_xticks(range(len(model_names)))
ax1.set_xticklabels(model_names, rotation=45, ha='right')
ax1.grid(True, alpha=0.3)

# 添加数值标签
for bar, value in zip(bars1, rmse_values):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height,
            f'{value:.4f}', ha='center', va='bottom', fontsize=9)

# MAE对比
mae_values = [results['mae'] for results in celsius_results.values()]
bars2 = ax2.bar(range(len(model_names)), mae_values, color=colors, alpha=0.7)
ax2.set_xlabel('模型')
ax2.set_ylabel('MAE (°C)')
ax2.set_title('模型MAE对比 (摄氏度)')
ax2.set_xticks(range(len(model_names)))
ax2.set_xticklabels(model_names, rotation=45, ha='right')
ax2.grid(True, alpha=0.3)

for bar, value in zip(bars2, mae_values):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height,
            f'{value:.4f}', ha='center', va='bottom', fontsize=9)

# 偏差对比
bias_values = [results['bias'] for results in celsius_results.values()]
bars3 = ax3.bar(range(len(model_names)), bias_values, color=colors, alpha=0.7)
ax3.set_xlabel('模型')
ax3.set_ylabel('偏差 (°C)')
ax3.set_title('模型偏差对比 (摄氏度)')
ax3.set_xticks(range(len(model_names)))
ax3.set_xticklabels(model_names, rotation=45, ha='right')
ax3.grid(True, alpha=0.3)
ax3.axhline(y=0, color='red', linestyle='--', alpha=0.8)

for bar, value in zip(bars3, bias_values):
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height,
            f'{value:.4f}', ha='center', va='bottom', fontsize=9)

# 相关系数对比
corr_values = [results['correlation'] for results in celsius_results.values()]
bars4 = ax4.bar(range(len(model_names)), corr_values, color=colors, alpha=0.7)
ax4.set_xlabel('模型')
ax4.set_ylabel('相关系数')
ax4.set_title('模型相关系数对比')
ax4.set_xticks(range(len(model_names)))
ax4.set_xticklabels(model_names, rotation=45, ha='right')
ax4.grid(True, alpha=0.3)
ax4.set_ylim(0.9, 1.0)  # 聚焦高相关性区域

for bar, value in zip(bars4, corr_values):
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height,
            f'{value:.4f}', ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.savefig('celsius_evaluation_results/celsius_performance_comparison.png', 
           dpi=300, bbox_inches='tight')
plt.close()

# 4. 生成最终评估报告
print("\n生成最终评估报告...")

report = f"""
# 改进模型摄氏度评估报告

## 评估概述
本报告展示了所有改进模型在测试集上的性能表现，所有指标均以摄氏度为单位。

## 模型性能排名

### RMSE排名 (越小越好)
"""

# 按RMSE排序
sorted_by_rmse = sorted(celsius_results.items(), key=lambda x: x[1]['rmse'])
for i, (model_key, results) in enumerate(sorted_by_rmse, 1):
    report += f"{i}. {results['name']}: {results['rmse']:.4f}°C\n"

report += f"""
### MAE排名 (越小越好)
"""

# 按MAE排序
sorted_by_mae = sorted(celsius_results.items(), key=lambda x: x[1]['mae'])
for i, (model_key, results) in enumerate(sorted_by_mae, 1):
    report += f"{i}. {results['name']}: {results['mae']:.4f}°C\n"

report += f"""
## 关键发现

1. **最佳RMSE模型**: {sorted_by_rmse[0][1]['name']} ({sorted_by_rmse[0][1]['rmse']:.4f}°C)
2. **最佳MAE模型**: {sorted_by_mae[0][1]['name']} ({sorted_by_mae[0][1]['mae']:.4f}°C)

## 详细分析

"""

for model_key, results in celsius_results.items():
    report += f"""
### {results['name']}
- **描述**: {results['description']}
- **RMSE**: {results['rmse']:.4f}°C
- **MAE**: {results['mae']:.4f}°C
- **偏差**: {results['bias']:.4f}°C
- **相关系数**: {results['correlation']:.4f}
- **预测范围**: {results['pred_range'][0]:.2f}°C - {results['pred_range'][1]:.2f}°C
"""

report += f"""
## 结论

基于摄氏度评估指标，改进策略的效果排序为：
1. {sorted_by_rmse[0][1]['name']} (最佳RMSE)
2. {sorted_by_rmse[1][1]['name']} (次佳RMSE)
3. {sorted_by_rmse[2][1]['name']} (第三RMSE)

生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

with open('celsius_evaluation_results/final_evaluation_report.md', 'w', encoding='utf-8') as f:
    f.write(report)

print("✅ 摄氏度评估完成！")
print("\n📁 输出文件:")
print("  - celsius_performance_comparison.csv: 详细性能对比表")
print("  - improvement_analysis.csv: 改善分析表")
print("  - celsius_performance_comparison.png: 性能对比图")
print("  - final_evaluation_report.md: 最终评估报告")

print(f"\n🏆 最佳模型: {sorted_by_rmse[0][1]['name']}")
print(f"   RMSE: {sorted_by_rmse[0][1]['rmse']:.4f}°C")
print(f"   MAE: {sorted_by_rmse[0][1]['mae']:.4f}°C")
