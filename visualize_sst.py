import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
import cartopy.crs as ccrs
import cartopy.feature as cfeature
import argparse
from datetime import datetime
import warnings

# 忽略cartopy下载警告
warnings.filterwarnings('ignore', category=UserWarning, module='cartopy')

# 设置中文字体和修复符号显示
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示

def parse_date(date_str):
    """解析日期字符串为datetime对象"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        raise argparse.ArgumentTypeError(f"无效的日期格式: '{date_str}'。请使用YYYY-MM-DD格式。")

def visualize_sst(date_str, output_file=None, show_plot=True):
    """可视化指定日期的SST数据"""
    print(f"正在可视化 {date_str} 的SST数据...")
    
    # 读取SST数据
    sst_data = xr.open_dataset('SST-V2.nc')
    
    # 选择最接近指定日期的时间点
    target_date = np.datetime64(date_str)
    time_index = abs(sst_data.time.values - target_date).argmin()
    selected_time = sst_data.time.values[time_index]
    
    # 提取该时间点的SST数据
    sst = sst_data.analysed_sst.sel(time=selected_time)
    
    # 将开尔文温度转换为摄氏度
    sst_celsius = sst - 273.15
    
    # 设置地图
    map_proj = ccrs.PlateCarree()
    
    # 设置地图特征
    coastlines = cfeature.NaturalEarthFeature('physical', 'coastline', '50m')
    land = cfeature.NaturalEarthFeature('physical', 'land', '50m', 
                                       edgecolor='black', facecolor='lightgray', alpha=0.5)
    
    # 创建图形
    plt.figure(figsize=(12, 9))
    ax = plt.axes(projection=map_proj)
    
    # 设置标题
    formatted_date = datetime.strptime(str(selected_time)[:10], '%Y-%m-%d').strftime('%Y-%m-%d')
    plt.title(f'真实SST场 ({formatted_date})', fontsize=16)
    
    # 添加地图特征
    ax.coastlines(resolution='50m')
    ax.add_feature(land)
    ax.gridlines(draw_labels=True, linewidth=0.5, color='gray', alpha=0.5, linestyle='--')
    
    # 设置经纬度范围
    ax.set_extent([sst_data.longitude.min(), sst_data.longitude.max(), 
                   sst_data.latitude.min(), sst_data.latitude.max()], crs=ccrs.PlateCarree())
    
    # 绘制SST数据
    # 使用与sst_comparison_day0_basic.png相同的颜色方案
    im = ax.pcolormesh(
        sst_data.longitude, 
        sst_data.latitude, 
        sst_celsius,
        cmap='plasma',  # 使用与参考图相同的色图
        transform=ccrs.PlateCarree()
    )
    
    # 添加颜色条
    cbar = plt.colorbar(im, orientation='vertical', pad=0.01, extend='both')
    cbar.set_label('温度 (°C)', fontsize=14)
    
    # 保存图像（如果指定了输出文件名）
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"图像已保存到 {output_file}")
    
    # 显示图像（如果需要）
    if show_plot:
        plt.show()
    
    # 关闭数据集
    sst_data.close()

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='可视化指定日期的SST数据')
    parser.add_argument('date', type=parse_date, 
                        help='要可视化的日期（YYYY-MM-DD格式）')
    parser.add_argument('--output', '-o', type=str, default=None,
                        help='输出图像文件的路径（如不指定则只显示不保存）')
    parser.add_argument('--no-show', action='store_true',
                        help='设置此选项以不显示图像（仅保存）')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 可视化SST数据
    visualize_sst(args.date.strftime('%Y-%m-%d'), args.output, not args.no_show)

if __name__ == '__main__':
    main() 