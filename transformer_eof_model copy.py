import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # Set font that supports Chinese
rcParams['axes.unicode_minus'] = False    # Fix negative sign display
import os
from sklearn.metrics import mean_squared_error, mean_absolute_error
import time

print("开始基于Transformer模型的EOF预测训练...")

# 创建结果文件夹
os.makedirs('model_results', exist_ok=True)
os.makedirs('model_figures', exist_ok=True)

# 设置随机种子确保可重复性
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载EOF分解后的PCs数据
print("加载EOF分解后的PCs数据...")
PCs_train = np.load('results/PCs_train.npy')  # 形状: (k, train_len)
PCs_val = np.load('results/PCs_val.npy')      # 形状: (k, val_len)
PCs_test = np.load('results/PCs_test.npy')    # 形状: (k, test_len)

k, train_len = PCs_train.shape
_, val_len = PCs_val.shape
_, test_len = PCs_test.shape

print(f"特征个数 k: {k}")
print(f"训练集长度: {train_len}")
print(f"验证集长度: {val_len}")
print(f"测试集长度: {test_len}")

# 定义滑动窗口数据集类
class TimeSeriesDataset(Dataset):
    def __init__(self, data, seq_len, pred_len):
        self.data = data
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.total_len = seq_len + pred_len
        
    def __len__(self):
        return self.data.shape[1] - self.total_len + 1
    
    def __getitem__(self, idx):
        # 输入序列: (seq_len, k)
        x = self.data[:, idx:idx+self.seq_len].T
        # 目标预测: (pred_len, k)
        y = self.data[:, idx+self.seq_len:idx+self.total_len].T
        return torch.FloatTensor(x), torch.FloatTensor(y)

# 滑动窗口参数
seq_len = 14   # 14天的输入序列
pred_len = 1   # 预测未来1天

# 创建数据集
train_dataset = TimeSeriesDataset(PCs_train, seq_len, pred_len)
val_dataset = TimeSeriesDataset(PCs_val, seq_len, pred_len)
test_dataset = TimeSeriesDataset(PCs_test, seq_len, pred_len)

# 创建数据加载器
batch_size = 32
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print(f"训练样本数: {len(train_dataset)}")
print(f"验证样本数: {len(val_dataset)}")
print(f"测试样本数: {len(test_dataset)}")

# Transformer编码器层
class TransformerEncoder(nn.Module):
    def __init__(self, feature_dim, d_model, nhead, dim_feedforward, dropout=0.1):
        super(TransformerEncoder, self).__init__()
        self.input_proj = nn.Linear(feature_dim, d_model)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=2)
        
    def forward(self, src):
        # src: [batch_size, seq_len, feature_dim]
        src = self.input_proj(src)  # [batch_size, seq_len, d_model]
        output = self.transformer_encoder(src)  # [batch_size, seq_len, d_model]
        return output

# 定义完整的预测模型
class EOFTransformerModel(nn.Module):
    def __init__(self, feature_dim, d_model, nhead, dim_feedforward, seq_len, pred_len, dropout=0.1):
        super(EOFTransformerModel, self).__init__()
        self.encoder = TransformerEncoder(
            feature_dim=feature_dim,
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout
        )
        self.fc = nn.Linear(d_model, feature_dim)
        self.seq_len = seq_len
        self.pred_len = pred_len
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim]
        enc_output = self.encoder(x)  # [batch_size, seq_len, d_model]
        # 仅使用最后一个时间步的输出进行预测
        output = self.fc(enc_output[:, -1:, :].repeat(1, self.pred_len, 1))  # [batch_size, pred_len, feature_dim]
        return output

# 定义训练函数
def train_epoch(model, train_loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    return total_loss / len(train_loader)

# 定义验证函数
def validate(model, val_loader, criterion, device):
    model.eval()
    total_loss = 0
    with torch.no_grad():
        for data, target in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            total_loss += loss.item()
    return total_loss / len(val_loader)

# 定义测试函数
def test(model, test_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    with torch.no_grad():
        for data, target in test_loader:
            data = data.to(device)
            output = model(data)
            all_preds.append(output.cpu().numpy())
            all_targets.append(target.numpy())
    
    # 将预测和目标转换为numpy数组
    all_preds = np.vstack(all_preds)
    all_targets = np.vstack(all_targets)
    
    # 计算MSE和MAE
    mse = mean_squared_error(all_targets.reshape(-1), all_preds.reshape(-1))
    mae = mean_absolute_error(all_targets.reshape(-1), all_preds.reshape(-1))
    
    return mse, mae, all_preds, all_targets

# 模型超参数
feature_dim = k        # 特征维度（模态数）
d_model = 64           # Transformer特征维度
nhead = 4              # 多头注意力头数
dim_feedforward = 128  # 前馈网络隐藏层维度
dropout = 0.1          # Dropout比率
learning_rate = 0.001  # 学习率
num_epochs = 50        # 训练轮数
patience = 10           # 早停耐心值

# 初始化模型
model = EOFTransformerModel(
    feature_dim=feature_dim,
    d_model=d_model,
    nhead=nhead,
    dim_feedforward=dim_feedforward,
    seq_len=seq_len,
    pred_len=pred_len,
    dropout=dropout
).to(device)

# 优化器和损失函数
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=learning_rate)
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=3, verbose=True)

# 训练模型
print("开始训练Transformer模型...")
start_time = time.time()

train_losses = []
val_losses = []
best_val_loss = float('inf')
early_stop_counter = 0
best_model_path = 'model_results/best_eof_transformer.pth'

for epoch in range(num_epochs):
    # 训练
    train_loss = train_epoch(model, train_loader, criterion, optimizer, device)
    train_losses.append(train_loss)
    
    # 验证
    val_loss = validate(model, val_loader, criterion, device)
    val_losses.append(val_loss)
    
    # 更新学习率
    scheduler.step(val_loss)
    
    # 保存最佳模型
    if val_loss < best_val_loss:
        best_val_loss = val_loss
        torch.save(model.state_dict(), best_model_path)
        early_stop_counter = 0
    else:
        early_stop_counter += 1
    
    # 早停
    if early_stop_counter >= patience:
        print(f"Early stopping at epoch {epoch+1}")
        break
    
    print(f"Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

training_time = time.time() - start_time
print(f"训练完成！总用时: {training_time:.2f}秒")

# 加载最佳模型并在测试集上评估
model.load_state_dict(torch.load(best_model_path))
test_mse, test_mae, test_preds, test_targets = test(model, test_loader, device)
print(f"测试集 MSE: {test_mse:.6f}, MAE: {test_mae:.6f}")

# 保存测试结果
np.save('model_results/test_predictions.npy', test_preds)
np.save('model_results/test_targets.npy', test_targets)

# 绘制训练和验证损失曲线
plt.figure(figsize=(10, 6))
plt.plot(train_losses, label='训练损失')
plt.plot(val_losses, label='验证损失')
plt.xlabel('轮次')
plt.ylabel('损失')
plt.title('训练和验证损失曲线')
plt.legend()
plt.grid(True)
plt.savefig('model_figures/training_curves.png', dpi=300, bbox_inches='tight')

# 绘制测试集预测结果（展示前3个模态的结果）
for i in range(min(3, k)):
    plt.figure(figsize=(12, 6))
    time_steps = range(len(test_preds))
    
    # 绘制实际值和预测值
    plt.plot(time_steps, test_targets[:, 0, i], label=f'真实值 PC{i+1}', color='blue')
    plt.plot(time_steps, test_preds[:, 0, i], label=f'预测值 PC{i+1}', color='red', alpha=0.7)
    
    plt.xlabel('时间步')
    plt.ylabel(f'PC{i+1}值')
    plt.title(f'模态{i+1}在测试集上的预测结果')
    plt.legend()
    plt.grid(True)
    plt.savefig(f'model_figures/test_predictions_pc{i+1}.png', dpi=300, bbox_inches='tight')

print("EOF时间系数预测训练完成！") 