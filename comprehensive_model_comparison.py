#!/usr/bin/env python3
"""
综合模型比较分析脚本
对比所有模型（包括纯SST基线模型）的性能表现
量化不同特征的贡献度

作者: AI Assistant
日期: 2025-07-02
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False
import seaborn as sns
import os
from datetime import datetime

print("🔍 开始综合模型比较分析...")

# 创建结果文件夹
os.makedirs('comprehensive_comparison', exist_ok=True)

# 加载标准化参数
sst_mean = np.load('sst_pcs_era5_data/sst_mean.npy')
sst_std = np.load('sst_pcs_era5_data/sst_std.npy')

def denormalize_and_convert_to_celsius(sst_norm):
    """反标准化并转换为摄氏度"""
    sst_kelvin = sst_norm * sst_std + sst_mean
    sst_celsius = sst_kelvin - 273.15
    return sst_celsius

# 定义所有模型的完整信息
all_models = {
    'sst_only': {
        'name': '纯SST模型 (基线)',
        'description': '仅使用SST历史序列',
        'input_features': 'SST',
        'channels': 1,
        'pred_file': 'sst_only_results/test_predictions_sst_only_basic.npy',
        'target_file': 'sst_only_results/test_targets_sst_only_basic.npy',
        'color': 'gray',
        'category': 'baseline'
    },
    'sst_pcs': {
        'name': 'SST+PCs模型',
        'description': 'SST + 主成分特征',
        'input_features': 'SST + PCs',
        'channels': 11,
        'pred_file': 'sst_pcs_convlstm_results/test_predictions_basic.npy',
        'target_file': 'sst_pcs_convlstm_results/test_targets_basic.npy',
        'color': 'blue',
        'category': 'single_addition'
    },
    'sst_era5': {
        'name': 'SST+ERA5模型 (消融)',
        'description': 'SST + ERA5大气变量',
        'input_features': 'SST + ERA5',
        'channels': 5,
        'pred_file': 'sst_era5_ablation_results/test_predictions_sst_era5_ablation_basic.npy',
        'target_file': 'sst_era5_ablation_results/test_targets_sst_era5_ablation_basic.npy',
        'color': 'red',
        'category': 'single_addition'
    },
    'sst_pcs_era5': {
        'name': 'SST+PCs+ERA5模型 (完整)',
        'description': 'SST + PCs + ERA5全特征',
        'input_features': 'SST + PCs + ERA5',
        'channels': 15,
        'pred_file': 'sst_pcs_era5_results/test_predictions_sst_pcs_era5_basic.npy',
        'target_file': 'sst_pcs_era5_results/test_targets_sst_pcs_era5_basic.npy',
        'color': 'green',
        'category': 'full_model'
    },
    'improved_selective': {
        'name': '改进模型-选择性PCs',
        'description': '优化的PCs选择策略',
        'input_features': 'SST + Selected PCs + ERA5',
        'channels': 10,
        'pred_file': 'improved_sst_pcs_era5_results/test_predictions_improved_selective_pcs.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_selective_pcs.npy',
        'color': 'orange',
        'category': 'improved'
    },
    'improved_weighted': {
        'name': '改进模型-加权融合',
        'description': '加权特征融合策略',
        'input_features': 'SST + Weighted PCs + ERA5',
        'channels': 15,
        'pred_file': 'improved_sst_pcs_era5_results/test_predictions_improved_weighted_fusion.npy',
        'target_file': 'improved_sst_pcs_era5_results/test_targets_improved_weighted_fusion.npy',
        'color': 'purple',
        'category': 'improved'
    }
}

# 加载所有模型的结果并计算指标
print("加载所有模型结果...")
model_results = {}

for model_key, model_info in all_models.items():
    try:
        # 加载预测结果
        predictions_norm = np.load(model_info['pred_file'])
        targets_norm = np.load(model_info['target_file'])
        
        # 转换为摄氏度
        predictions_celsius = denormalize_and_convert_to_celsius(predictions_norm)
        targets_celsius = denormalize_and_convert_to_celsius(targets_norm)
        
        # 计算评估指标
        error = predictions_celsius - targets_celsius
        rmse = np.sqrt(np.mean(error**2))
        mae = np.mean(np.abs(error))
        bias = np.mean(error)
        std_error = np.std(error)
        correlation = np.corrcoef(predictions_celsius.flatten(), targets_celsius.flatten())[0, 1]
        
        # 存储结果
        model_results[model_key] = {
            'info': model_info,
            'rmse': rmse,
            'mae': mae,
            'bias': bias,
            'std_error': std_error,
            'correlation': correlation,
            'predictions': predictions_celsius,
            'targets': targets_celsius
        }
        
        print(f"✅ {model_info['name']}: RMSE={rmse:.4f}°C, MAE={mae:.4f}°C")
        
    except FileNotFoundError:
        print(f"⚠️  未找到 {model_info['name']} 的结果文件")
        continue

if not model_results:
    print("❌ 未找到任何模型结果")
    exit(1)

print(f"成功加载 {len(model_results)} 个模型的结果")

# 计算特征贡献度分析
print("\n📊 计算特征贡献度...")

# 以纯SST模型为基线
if 'sst_only' in model_results:
    baseline_rmse = model_results['sst_only']['rmse']
    baseline_mae = model_results['sst_only']['mae']
    
    print(f"基线模型 (纯SST): RMSE={baseline_rmse:.4f}°C, MAE={baseline_mae:.4f}°C")
    
    # 计算各特征的贡献
    feature_contributions = {}
    
    for model_key, results in model_results.items():
        if model_key != 'sst_only':
            rmse_improvement = ((baseline_rmse - results['rmse']) / baseline_rmse) * 100
            mae_improvement = ((baseline_mae - results['mae']) / baseline_mae) * 100
            
            feature_contributions[model_key] = {
                'rmse_improvement': rmse_improvement,
                'mae_improvement': mae_improvement,
                'rmse_absolute': baseline_rmse - results['rmse'],
                'mae_absolute': baseline_mae - results['mae']
            }
            
            print(f"{results['info']['name']}:")
            print(f"  RMSE改善: {rmse_improvement:+.2f}% ({results['rmse']:.4f}°C)")
            print(f"  MAE改善: {mae_improvement:+.2f}% ({results['mae']:.4f}°C)")

# 创建综合性能对比表
print("\n📋 创建综合性能对比表...")
comparison_data = []

for model_key, results in model_results.items():
    info = results['info']
    
    # 计算相对于基线的改善
    if 'sst_only' in model_results and model_key != 'sst_only':
        rmse_improvement = feature_contributions[model_key]['rmse_improvement']
        mae_improvement = feature_contributions[model_key]['mae_improvement']
    else:
        rmse_improvement = 0.0
        mae_improvement = 0.0
    
    comparison_data.append({
        '模型名称': info['name'],
        '输入特征': info['input_features'],
        '通道数': info['channels'],
        'RMSE (°C)': f"{results['rmse']:.4f}",
        'MAE (°C)': f"{results['mae']:.4f}",
        'RMSE改善 (%)': f"{rmse_improvement:+.2f}",
        'MAE改善 (%)': f"{mae_improvement:+.2f}",
        '偏差 (°C)': f"{results['bias']:.4f}",
        '相关系数': f"{results['correlation']:.4f}",
        '模型类别': info['category']
    })

df_comparison = pd.DataFrame(comparison_data)

# 按RMSE排序
df_comparison_sorted = df_comparison.sort_values('RMSE (°C)')
df_comparison_sorted.to_csv('comprehensive_comparison/comprehensive_performance_comparison.csv', 
                           index=False, encoding='utf-8-sig')

print("✅ 综合性能对比表已保存")

# 绘制综合性能对比图
print("\n📈 绘制综合性能对比图...")

# 1. 性能对比柱状图
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

model_names = [results['info']['name'] for results in model_results.values()]
rmse_values = [results['rmse'] for results in model_results.values()]
mae_values = [results['mae'] for results in model_results.values()]
colors = [results['info']['color'] for results in model_results.values()]

# RMSE对比
bars1 = ax1.bar(range(len(model_names)), rmse_values, color=colors, alpha=0.7)
ax1.set_xlabel('模型')
ax1.set_ylabel('RMSE (°C)')
ax1.set_title('模型RMSE对比')
ax1.set_xticks(range(len(model_names)))
ax1.set_xticklabels([name.replace('模型', '') for name in model_names], rotation=45, ha='right')
ax1.grid(True, alpha=0.3)

for bar, value in zip(bars1, rmse_values):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height,
            f'{value:.4f}', ha='center', va='bottom', fontsize=9)

# MAE对比
bars2 = ax2.bar(range(len(model_names)), mae_values, color=colors, alpha=0.7)
ax2.set_xlabel('模型')
ax2.set_ylabel('MAE (°C)')
ax2.set_title('模型MAE对比')
ax2.set_xticks(range(len(model_names)))
ax2.set_xticklabels([name.replace('模型', '') for name in model_names], rotation=45, ha='right')
ax2.grid(True, alpha=0.3)

for bar, value in zip(bars2, mae_values):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height,
            f'{value:.4f}', ha='center', va='bottom', fontsize=9)

# 特征贡献度分析
if 'sst_only' in model_results:
    contrib_models = [key for key in feature_contributions.keys()]
    contrib_names = [model_results[key]['info']['name'].replace('模型', '') for key in contrib_models]
    rmse_improvements = [feature_contributions[key]['rmse_improvement'] for key in contrib_models]
    mae_improvements = [feature_contributions[key]['mae_improvement'] for key in contrib_models]
    contrib_colors = [model_results[key]['info']['color'] for key in contrib_models]
    
    # RMSE改善
    bars3 = ax3.bar(range(len(contrib_names)), rmse_improvements, color=contrib_colors, alpha=0.7)
    ax3.set_xlabel('模型')
    ax3.set_ylabel('RMSE改善 (%)')
    ax3.set_title('相对于纯SST基线的RMSE改善')
    ax3.set_xticks(range(len(contrib_names)))
    ax3.set_xticklabels(contrib_names, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.8)
    
    for bar, value in zip(bars3, rmse_improvements):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height,
                f'{value:+.1f}%', ha='center', va='bottom' if value >= 0 else 'top', fontsize=9)
    
    # MAE改善
    bars4 = ax4.bar(range(len(contrib_names)), mae_improvements, color=contrib_colors, alpha=0.7)
    ax4.set_xlabel('模型')
    ax4.set_ylabel('MAE改善 (%)')
    ax4.set_title('相对于纯SST基线的MAE改善')
    ax4.set_xticks(range(len(contrib_names)))
    ax4.set_xticklabels(contrib_names, rotation=45, ha='right')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.8)
    
    for bar, value in zip(bars4, mae_improvements):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
                f'{value:+.1f}%', ha='center', va='bottom' if value >= 0 else 'top', fontsize=9)

plt.tight_layout()
plt.savefig('comprehensive_comparison/comprehensive_performance_comparison.png', 
           dpi=300, bbox_inches='tight')
plt.close()

# 2. 特征通道数 vs 性能散点图
plt.figure(figsize=(12, 8))

channels = [results['info']['channels'] for results in model_results.values()]
rmse_vals = [results['rmse'] for results in model_results.values()]
model_names = [results['info']['name'] for results in model_results.values()]
colors = [results['info']['color'] for results in model_results.values()]

plt.scatter(channels, rmse_vals, c=colors, s=100, alpha=0.7)

for i, name in enumerate(model_names):
    plt.annotate(name.replace('模型', ''), (channels[i], rmse_vals[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=9)

plt.xlabel('输入通道数')
plt.ylabel('RMSE (°C)')
plt.title('输入通道数 vs RMSE性能')
plt.grid(True, alpha=0.3)
plt.savefig('comprehensive_comparison/channels_vs_performance.png', 
           dpi=300, bbox_inches='tight')
plt.close()

# 生成最终分析报告
print("\n📄 生成最终分析报告...")

# 找出最佳模型
best_rmse_model = min(model_results.items(), key=lambda x: x[1]['rmse'])
best_mae_model = min(model_results.items(), key=lambda x: x[1]['mae'])

report = f"""
# 综合模型比较分析报告

## 实验概述
本报告对比了从最简单的纯SST基线模型到复杂的多特征融合模型的性能表现。

## 模型性能排名

### RMSE排名 (°C)
"""

# 按RMSE排序
sorted_models = sorted(model_results.items(), key=lambda x: x[1]['rmse'])
for i, (model_key, results) in enumerate(sorted_models, 1):
    report += f"{i}. {results['info']['name']}: {results['rmse']:.4f}°C\n"

report += f"""
### MAE排名 (°C)
"""

# 按MAE排序
sorted_models_mae = sorted(model_results.items(), key=lambda x: x[1]['mae'])
for i, (model_key, results) in enumerate(sorted_models_mae, 1):
    report += f"{i}. {results['info']['name']}: {results['mae']:.4f}°C\n"

if 'sst_only' in model_results:
    report += f"""
## 特征贡献度分析

基线模型 (纯SST): RMSE={baseline_rmse:.4f}°C, MAE={baseline_mae:.4f}°C

### 各特征组合的贡献:
"""
    
    for model_key, contrib in feature_contributions.items():
        model_name = model_results[model_key]['info']['name']
        report += f"""
**{model_name}**:
- RMSE改善: {contrib['rmse_improvement']:+.2f}% ({contrib['rmse_absolute']:+.4f}°C)
- MAE改善: {contrib['mae_improvement']:+.2f}% ({contrib['mae_absolute']:+.4f}°C)
"""

report += f"""
## 关键发现

1. **最佳RMSE模型**: {best_rmse_model[1]['info']['name']} ({best_rmse_model[1]['rmse']:.4f}°C)
2. **最佳MAE模型**: {best_mae_model[1]['info']['name']} ({best_mae_model[1]['mae']:.4f}°C)

## 结论与建议

基于综合分析结果，推荐使用性能最佳的模型进行实际应用。

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

with open('comprehensive_comparison/comprehensive_analysis_report.md', 'w', encoding='utf-8') as f:
    f.write(report)

print("✅ 综合模型比较分析完成！")
print("\n📁 输出文件:")
print("  - comprehensive_performance_comparison.csv: 详细性能对比表")
print("  - comprehensive_performance_comparison.png: 性能对比图")
print("  - channels_vs_performance.png: 通道数vs性能散点图")
print("  - comprehensive_analysis_report.md: 综合分析报告")

print(f"\n🏆 推荐模型: {best_rmse_model[1]['info']['name']}")
print(f"   RMSE: {best_rmse_model[1]['rmse']:.4f}°C")
print(f"   MAE: {best_rmse_model[1]['mae']:.4f}°C")
