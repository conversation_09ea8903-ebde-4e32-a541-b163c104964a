# 可视化样式统一示例

## 使用统一配置的示例代码

```python
from visualization_config import (
    SST_COLORMAP, SST_VMIN, SST_VMAX,
    ERROR_COLORMAP, ERROR_VMIN, ERROR_VMAX,
    FIGURE_SIZES, FONT_SIZES, DPI,
    get_sample_date_mapping
)

# 获取日期映射
date_mapping = get_sample_date_mapping(len(predictions), seq_len=14)
sample_date = date_mapping.get(sample_idx, f'样本{sample_idx}')

# 绘制SST场
fig, axes = plt.subplots(1, 3, figsize=FIGURE_SIZES['triple_comparison'])

im1 = axes[0].imshow(sst_data, cmap=SST_COLORMAP, 
                    vmin=SST_VMIN, vmax=SST_VMAX)
axes[0].set_title(f'真实SST (°C)\n{sample_date}', 
                 fontsize=FONT_SIZES['title'])

# 保存图片
plt.savefig('output.png', dpi=DPI, bbox_inches='tight')
```

## 统一的颜色方案

- **SST场**: plasma色图 (20-32°C)
- **误差场**: RdBu_r色图 (-2到+2°C)  
- **RMSE场**: YlOrRd色图 (0-1°C)
- **时间序列**: 蓝色(真实值), 橙色(预测值), 红色(误差)

## 标准图表尺寸

- **三联对比图**: (18, 6)
- **单个地图**: (12, 8)
- **时间序列**: (12, 6)
- **性能对比**: (15, 10)
