# EOF分解与Transformer预测模型

这个项目实现了使用EOF（经验正交函数）分解和Transformer深度学习模型对多变量地球物理场进行分析和预测。

## 项目结构

- `preprocess_and_eof.py`: 数据预处理和EOF分解
- `transformer_eof_model.py`: 基于Transformer的预测模型训练
- `predict_and_reconstruct.py`: 使用训练好的模型进行预测并重构物理场
- `run_all.py`: 工作流程自动化脚本，支持模型比较功能
- `requirements.txt`: 需要安装的Python依赖包

## 数据文件

需要以下输入数据文件:
- `SST-V2.nc`: 海表温度数据
- `data_ERA5.nc`: ERA5再分析数据（包含2m温度）

## 工作流程

### 1. 数据预处理和EOF分解

运行以下命令进行数据预处理和EOF分解:

```bash
python preprocess_and_eof.py
```

这个脚本会:
- 读取原始SST和ERA5数据
- 将ERA5数据插值到SST网格
- 归一化数据
- 进行EOF分解
- 保存EOF模态和时间系数
- 生成相关可视化图表

输出文件将保存在`results/`和`figures/`目录中。

### 2. 训练Transformer模型

运行以下命令训练Transformer模型:

```bash
python transformer_eof_model.py
```

这个脚本会:
- 加载EOF分解的时间系数
- 对主成分时间序列进行标准化处理
- 使用滑动窗口创建时序训练样本
- 训练基于Transformer的预测模型
- 在验证集上调整超参数
- 在测试集上评估模型性能
- 保存训练好的模型和性能评估结果

输出文件将保存在`model_results/`和`model_figures/`目录中。

### 3. 预测和重构物理场

运行以下命令进行预测和重构:

```bash
python predict_and_reconstruct.py
```

这个脚本会:
- 加载训练好的Transformer模型
- 在测试数据上进行预测
- 重构物理场（SST和T2m）
- 计算和可视化预测误差
- 分析预测性能

输出文件将保存在`reconstruction_results/`和`reconstruction_figures/`目录中。

### 4. 运行完整工作流程（可选参数）

使用`run_all.py`脚本可以自动运行完整工作流程:

```bash
# 运行基本工作流程
python run_all.py

# 重新运行数据预处理和EOF分解（默认会跳过此步骤）
python run_all.py --preprocessing

# 保存当前模型作为旧模型，供后续比较
python run_all.py --save_old

# 比较新旧模型的性能
python run_all.py --compare
```

## 模型改进

最新版本的模型包含以下改进：

1. **数据标准化**：对EOF时间系数进行标准化处理，使所有特征具有相同的尺度
2. **增强的模型架构**：
   - 更深的Transformer编码器（3层）
   - 更大的特征维度（128）和前馈网络（512）
   - 增加头数（8个）提高并行注意力能力
   - 添加残差连接保持时序信息
3. **训练策略改进**：
   - 使用AdamW优化器和权重衰减正则化
   - 梯度裁剪防止梯度爆炸
   - 动态学习率调整
   - 更长的早停耐心值
4. **模型比较功能**：支持新旧模型性能比较

## 安装依赖

安装所需依赖:

```bash
pip install -r requirements.txt
```

## 超参数调整

在`transformer_eof_model.py`中可以调整以下超参数:
- `seq_len`: 输入序列长度（默认14）
- `pred_len`: 预测序列长度（默认1）
- `d_model`: Transformer特征维度（128）
- `nhead`: 多头注意力头数（8）
- `dim_feedforward`: 前馈网络维度（512）
- `num_encoder_layers`: 编码器层数（3）
- `batch_size`: 批处理大小（64）
- `learning_rate`: 学习率（0.0005）
- `weight_decay`: 权重衰减（0.0001）
- `num_epochs`: 训练轮数（100）
- `patience`: 早停耐心值（15）

## 性能评估

模型性能通过以下指标评估：
- 测试集上的均方误差（MSE）
- 测试集上的平均绝对误差（MAE）
- 物理场重构误差的空间分布
- 新旧模型性能比较图表 