import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import os
import time
from sklearn.metrics import mean_squared_error, mean_absolute_error
from convlstm_model import IMFConvLSTM

print("开始基于ConvLSTM模型的IMF预测训练...")

# 创建结果文件夹
os.makedirs('convlstm_results', exist_ok=True)
os.makedirs('convlstm_figures', exist_ok=True)

# 设置随机种子确保可重复性
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载MEEMD分解后的IMF数据
print("加载MEEMD分解后的IMF数据...")
train_imfs = np.load('meemd_results/train_imfs.npy')  # 形状: (num_imfs, train_len, lat, lon)
val_imfs = np.load('meemd_results/val_imfs.npy')      # 形状: (num_imfs, val_len, lat, lon)
test_imfs = np.load('meemd_results/test_imfs.npy')    # 形状: (num_imfs, test_len, lat, lon)

# 加载归一化参数
sst_mean = np.load('meemd_results/sst_mean.npy')
sst_std = np.load('meemd_results/sst_std.npy')

num_imfs, train_len, n_lat, n_lon = train_imfs.shape
_, val_len, _, _ = val_imfs.shape
_, test_len, _, _ = test_imfs.shape

print(f"IMF分量数: {num_imfs}")
print(f"训练集长度: {train_len}")
print(f"验证集长度: {val_len}")
print(f"测试集长度: {test_len}")
print(f"空间维度: {n_lat} x {n_lon}")

# 定义滑动窗口数据集类
class IMFDataset(Dataset):
    def __init__(self, imfs, seq_len, pred_len):
        self.imfs = imfs  # 形状: (num_imfs, time, lat, lon)
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.total_len = seq_len + pred_len
        
    def __len__(self):
        return self.imfs.shape[1] - self.total_len + 1
    
    def __getitem__(self, idx):
        # 输入序列: (seq_len, num_imfs, lat, lon)
        x = self.imfs[:, idx:idx+self.seq_len].transpose(1, 0, 2, 3)
        # 目标预测: (pred_len, lat, lon)
        y = np.sum(self.imfs[:, idx+self.seq_len:idx+self.total_len], axis=0)
        return torch.FloatTensor(x), torch.FloatTensor(y)

# 滑动窗口参数
seq_len = 14  # 14天的输入序列
pred_len = 1   # 预测未来1天

# 创建数据集
train_dataset = IMFDataset(train_imfs, seq_len, pred_len)
val_dataset = IMFDataset(val_imfs, seq_len, pred_len)
test_dataset = IMFDataset(test_imfs, seq_len, pred_len)

# 创建数据加载器
batch_size = 32
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print(f"训练样本数: {len(train_dataset)}")
print(f"验证样本数: {len(val_dataset)}")
print(f"测试样本数: {len(test_dataset)}")

# 初始化模型
model = IMFConvLSTM(
    num_imfs=num_imfs,
    seq_len=seq_len,
    pred_len=pred_len,
    hidden_channels=64,
    kernel_size=3,
    num_layers=2
).to(device)

# 打印模型结构
print("模型结构:")
print(model)
total_params = sum(p.numel() for p in model.parameters())
print(f"模型总参数量: {total_params}")

# 优化器和损失函数
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)
scheduler = optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, mode='min', factor=0.5, patience=5, verbose=True, min_lr=1e-6
)

# 训练函数
def train_epoch(model, train_loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    return total_loss / len(train_loader)

# 验证函数
def validate(model, val_loader, criterion, device):
    model.eval()
    total_loss = 0
    with torch.no_grad():
        for data, target in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            total_loss += loss.item()
    return total_loss / len(val_loader)

# 训练模型
print("开始训练ConvLSTM模型...")
start_time = time.time()

num_epochs = 50
train_losses = []
val_losses = []
best_val_loss = float('inf')
early_stop_counter = 0
patience = 10
best_model_path = 'convlstm_results/best_convlstm.pth'

for epoch in range(num_epochs):
    # 训练
    train_loss = train_epoch(model, train_loader, criterion, optimizer, device)
    train_losses.append(train_loss)
    
    # 验证
    val_loss = validate(model, val_loader, criterion, device)
    val_losses.append(val_loss)
    
    # 更新学习率
    scheduler.step(val_loss)
    
    # 保存最佳模型
    if val_loss < best_val_loss:
        best_val_loss = val_loss
        torch.save(model.state_dict(), best_model_path)
        early_stop_counter = 0
        print(f"Epoch {epoch+1}: 保存新的最佳模型, 验证损失: {val_loss:.6f}")
    else:
        early_stop_counter += 1
    
    # 早停
    if early_stop_counter >= patience:
        print(f"Early stopping at epoch {epoch+1}")
        break
    
    print(f"Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

training_time = time.time() - start_time
print(f"训练完成！总用时: {training_time:.2f}秒")

# 绘制训练和验证损失曲线
plt.figure(figsize=(10, 6))
plt.plot(train_losses, label='训练损失')
plt.plot(val_losses, label='验证损失')
plt.xlabel('轮次')
plt.ylabel('损失')
plt.title('训练和验证损失曲线')
plt.legend()
plt.grid(True)
plt.savefig('convlstm_figures/training_curves.png', dpi=300, bbox_inches='tight')
plt.close()

# 加载最佳模型并在测试集上评估
print("在测试集上评估模型...")
model.load_state_dict(torch.load(best_model_path))
model.eval()

# 在测试集上进行预测
all_preds = []
all_targets = []

with torch.no_grad():
    for data, target in test_loader:
        data = data.to(device)
        output = model(data)
        all_preds.append(output.cpu().numpy())
        all_targets.append(target.numpy())

# 将预测和目标转换为numpy数组
all_preds = np.vstack(all_preds)
all_targets = np.vstack(all_targets)

# 计算误差
test_mse = mean_squared_error(all_targets.reshape(-1), all_preds.reshape(-1))
test_mae = mean_absolute_error(all_targets.reshape(-1), all_preds.reshape(-1))
print(f"测试集 MSE(标准化尺度): {test_mse:.6f}, MAE(标准化尺度): {test_mae:.6f}")

# 保存测试结果
np.save('convlstm_results/test_predictions.npy', all_preds)
np.save('convlstm_results/test_targets.npy', all_targets)

print("ConvLSTM模型训练与评估完成！") 