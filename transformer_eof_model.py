import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # Set font that supports Chinese
rcParams['axes.unicode_minus'] = False    # Fix negative sign display
import os
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import time

print("开始基于Transformer模型的EOF预测训练...")

# 创建结果文件夹
os.makedirs('model_results', exist_ok=True)
os.makedirs('model_figures', exist_ok=True)

# 设置随机种子确保可重复性
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU可用性
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载EOF分解后的PCs数据
print("加载EOF分解后的PCs数据...")
PCs_train = np.load('results/PCs_train.npy')  # 形状: (k, train_len)
PCs_val = np.load('results/PCs_val.npy')      # 形状: (k, val_len)
PCs_test = np.load('results/PCs_test.npy')    # 形状: (k, test_len)

k, train_len = PCs_train.shape
_, val_len = PCs_val.shape
_, test_len = PCs_test.shape

print(f"特征个数 k: {k}")
print(f"训练集长度: {train_len}")
print(f"验证集长度: {val_len}")
print(f"测试集长度: {test_len}")

# 新增: 对PCs数据进行标准化处理
print("对PCs数据进行标准化处理...")
scaler = StandardScaler()
# 转置以便按特征进行标准化
PCs_train_flat = PCs_train.T
# 使用训练集拟合scaler
scaler.fit(PCs_train_flat)
# 变换所有数据集
PCs_train_scaled = scaler.transform(PCs_train_flat).T
PCs_val_scaled = scaler.transform(PCs_val.T).T
PCs_test_scaled = scaler.transform(PCs_test.T).T
# 保存scaler供后续使用
np.save('model_results/pc_scaler_mean.npy', scaler.mean_)
np.save('model_results/pc_scaler_scale.npy', scaler.scale_)

print("标准化前PCs范围:")
print(f"训练集: {PCs_train.min()} - {PCs_train.max()}")
print(f"验证集: {PCs_val.min()} - {PCs_val.max()}")
print(f"测试集: {PCs_test.min()} - {PCs_test.max()}")

print("标准化后PCs范围:")
print(f"训练集: {PCs_train_scaled.min()} - {PCs_train_scaled.max()}")
print(f"验证集: {PCs_val_scaled.min()} - {PCs_val_scaled.max()}")
print(f"测试集: {PCs_test_scaled.min()} - {PCs_test_scaled.max()}")

# 定义滑动窗口数据集类
class TimeSeriesDataset(Dataset):
    def __init__(self, data, seq_len, pred_len):
        self.data = data
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.total_len = seq_len + pred_len
        
    def __len__(self):
        return self.data.shape[1] - self.total_len + 1
    
    def __getitem__(self, idx):
        # 输入序列: (seq_len, k)
        x = self.data[:, idx:idx+self.seq_len].T
        # 目标预测: (pred_len, k)
        y = self.data[:, idx+self.seq_len:idx+self.total_len].T
        return torch.FloatTensor(x), torch.FloatTensor(y)

# 滑动窗口参数
seq_len = 14  # 14天的输入序列
pred_len = 1   # 预测未来1天

# 创建数据集 - 使用标准化后的数据
train_dataset = TimeSeriesDataset(PCs_train_scaled, seq_len, pred_len)
val_dataset = TimeSeriesDataset(PCs_val_scaled, seq_len, pred_len)
test_dataset = TimeSeriesDataset(PCs_test_scaled, seq_len, pred_len)

# 创建数据加载器
batch_size = 64  # 增大批次大小以提高训练稳定性
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print(f"训练样本数: {len(train_dataset)}")
print(f"验证样本数: {len(val_dataset)}")
print(f"测试样本数: {len(test_dataset)}")

# 改进的Transformer编码器层
class TransformerEncoder(nn.Module):
    def __init__(self, feature_dim, d_model, nhead, dim_feedforward, num_layers=2, dropout=0.1):
        super(TransformerEncoder, self).__init__()
        self.input_proj = nn.Linear(feature_dim, d_model)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, src, src_mask=None):
        # src: [batch_size, seq_len, feature_dim]
        src = self.input_proj(src)  # [batch_size, seq_len, d_model]
        output = self.transformer_encoder(src, src_mask)  # [batch_size, seq_len, d_model]
        output = self.norm(output)
        return output

# 改进的预测模型 - 添加残差连接和多层结构
class EOFTransformerModel(nn.Module):
    def __init__(self, feature_dim, d_model, nhead, dim_feedforward, seq_len, pred_len, num_encoder_layers=3, dropout=0.1):
        super(EOFTransformerModel, self).__init__()
        self.encoder = TransformerEncoder(
            feature_dim=feature_dim,
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            num_layers=num_encoder_layers,
            dropout=dropout
        )
        
        # 序列到序列预测层，更复杂的解码结构
        self.pred_projection = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, feature_dim)
        )
        
        # 添加残差连接所需的投影层
        self.residual_proj = nn.Linear(feature_dim, feature_dim)
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim]
        batch_size = x.size(0)
        
        # 编码整个序列
        enc_output = self.encoder(x)  # [batch_size, seq_len, d_model]
        
        # 使用最后一个时间步作为解码器输入
        last_hidden = enc_output[:, -1:, :]  # [batch_size, 1, d_model]
        
        # 创建预测输出
        outputs = []
        # 残差连接使用输入序列的最后一个时间步
        residual = x[:, -1:, :]  # [batch_size, 1, feature_dim]
        
        # 只预测一个时间步（pred_len=1）的情况下可以简化
        if self.pred_len == 1:
            # 预测 + 残差连接
            pred = self.pred_projection(last_hidden)
            residual_mapped = self.residual_proj(residual)
            output = pred + residual_mapped
            return output
        
        # 如果需要预测多个时间步，使用自回归方式
        for _ in range(self.pred_len):
            # 预测 + 残差
            pred = self.pred_projection(last_hidden)
            residual_mapped = self.residual_proj(residual)
            curr_pred = pred + residual_mapped
            
            # 添加到输出列表
            outputs.append(curr_pred)
            
            # 更新残差值和隐藏状态用于下一步预测
            residual = curr_pred
            # 下一步预测需要使用新的编码器输出，简化实现直接使用最后一个时间步
        
        # 拼接所有预测结果
        if self.pred_len > 1:
            output = torch.cat(outputs, dim=1)  # [batch_size, pred_len, feature_dim]
        
        return output

# 改进的训练函数 - 添加梯度裁剪
def train_epoch(model, train_loader, criterion, optimizer, device, clip_grad=1.0):
    model.train()
    total_loss = 0
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        
        # 添加梯度裁剪避免梯度爆炸
        torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)
        
        optimizer.step()
        total_loss += loss.item()
    return total_loss / len(train_loader)

# 定义验证函数
def validate(model, val_loader, criterion, device):
    model.eval()
    total_loss = 0
    with torch.no_grad():
        for data, target in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)
            total_loss += loss.item()
    return total_loss / len(val_loader)

# 定义测试函数，添加反标准化功能
def test(model, test_loader, device, scaler=None):
    model.eval()
    all_preds = []
    all_targets = []
    with torch.no_grad():
        for data, target in test_loader:
            data = data.to(device)
            output = model(data)
            all_preds.append(output.cpu().numpy())
            all_targets.append(target.numpy())
    
    # 将预测和目标转换为numpy数组
    all_preds = np.vstack(all_preds)
    all_targets = np.vstack(all_targets)
    
    # 计算标准化空间中的MSE和MAE
    scaled_mse = mean_squared_error(all_targets.reshape(-1), all_preds.reshape(-1))
    scaled_mae = mean_absolute_error(all_targets.reshape(-1), all_preds.reshape(-1))
    
    # 返回预测结果和评估指标
    return scaled_mse, scaled_mae, all_preds, all_targets

# 反标准化函数，用于还原真实尺度的预测值
def inverse_transform_pcs(scaled_pcs, scaler):
    # 输入形状为 (samples, pred_len, k)
    # 需要转换为 (samples*pred_len, k) 进行反变换
    samples, pred_len, k = scaled_pcs.shape
    flat_pcs = scaled_pcs.reshape(-1, k)
    
    # 反向变换
    mean = scaler.mean_
    scale = scaler.scale_
    
    # 手动进行反标准化 (x * scale + mean)
    inverse_flat = flat_pcs * scale + mean
    
    # 恢复原始形状
    inverse_pcs = inverse_flat.reshape(samples, pred_len, k)
    return inverse_pcs

# 模型超参数 - 调整以提高性能
feature_dim = k            # 特征维度（模态数）
d_model = 128              # 增大Transformer特征维度
nhead = 8                  # 增加多头注意力头数
dim_feedforward = 512      # 增大前馈网络隐藏层维度
num_encoder_layers = 3     # 增加编码器层数
dropout = 0.2              # 增加Dropout比率防止过拟合
learning_rate = 0.0005     # 降低学习率提高稳定性
weight_decay = 1e-4        # 添加L2正则化
num_epochs = 100           # 增加最大训练轮数
patience = 15              # 增加早停耐心值
clip_grad = 1.0            # 梯度裁剪阈值

# 初始化模型
model = EOFTransformerModel(
    feature_dim=feature_dim,
    d_model=d_model,
    nhead=nhead,
    dim_feedforward=dim_feedforward,
    seq_len=seq_len,
    pred_len=pred_len,
    num_encoder_layers=num_encoder_layers,
    dropout=dropout
).to(device)

# 打印模型结构
print("模型结构:")
print(model)
total_params = sum(p.numel() for p in model.parameters())
print(f"模型总参数量: {total_params}")

# 优化器和损失函数
criterion = nn.MSELoss()
optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
scheduler = optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, mode='min', factor=0.5, patience=5, verbose=True, min_lr=1e-6
)

# 训练模型
print("开始训练Transformer模型...")
start_time = time.time()

train_losses = []
val_losses = []
best_val_loss = float('inf')
early_stop_counter = 0
best_model_path = 'model_results/best_eof_transformer.pth'

for epoch in range(num_epochs):
    # 训练
    train_loss = train_epoch(model, train_loader, criterion, optimizer, device, clip_grad)
    train_losses.append(train_loss)
    
    # 验证
    val_loss = validate(model, val_loader, criterion, device)
    val_losses.append(val_loss)
    
    # 更新学习率
    scheduler.step(val_loss)
    
    # 保存最佳模型
    if val_loss < best_val_loss:
        best_val_loss = val_loss
        torch.save(model.state_dict(), best_model_path)
        early_stop_counter = 0
        print(f"Epoch {epoch+1}: 保存新的最佳模型, 验证损失: {val_loss:.6f}")
    else:
        early_stop_counter += 1
    
    # 早停
    if early_stop_counter >= patience:
        print(f"Early stopping at epoch {epoch+1}")
        break
    
    print(f"Epoch {epoch+1}/{num_epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

training_time = time.time() - start_time
print(f"训练完成！总用时: {training_time:.2f}秒")

# 加载最佳模型并在测试集上评估
model.load_state_dict(torch.load(best_model_path))
test_mse, test_mae, test_preds, test_targets = test(model, test_loader, device)
print(f"测试集 MSE(标准化尺度): {test_mse:.6f}, MAE(标准化尺度): {test_mae:.6f}")

# 反标准化预测结果和真实值
print("反标准化测试集预测结果...")
# 使用StandardScaler对象反变换
inverse_preds = inverse_transform_pcs(test_preds, scaler)
inverse_targets = inverse_transform_pcs(test_targets, scaler)

# 计算反标准化后的误差
inverse_mse = mean_squared_error(inverse_targets.reshape(-1), inverse_preds.reshape(-1))
inverse_mae = mean_absolute_error(inverse_targets.reshape(-1), inverse_preds.reshape(-1))
print(f"测试集 MSE(原始尺度): {inverse_mse:.6f}, MAE(原始尺度): {inverse_mae:.6f}")

# 保存测试结果
np.save('model_results/test_predictions.npy', inverse_preds)
np.save('model_results/test_targets.npy', inverse_targets)
np.save('model_results/test_predictions_scaled.npy', test_preds)
np.save('model_results/test_targets_scaled.npy', test_targets)

# 绘制训练和验证损失曲线
plt.figure(figsize=(10, 6))
plt.plot(train_losses, label='训练损失')
plt.plot(val_losses, label='验证损失')
plt.xlabel('轮次')
plt.ylabel('损失')
plt.title('训练和验证损失曲线')
plt.legend()
plt.grid(True)
plt.savefig('model_figures/training_curves.png', dpi=300, bbox_inches='tight')

# 绘制测试集预测结果（展示前3个模态的结果）- 使用原始尺度
for i in range(min(3, k)):
    plt.figure(figsize=(12, 6))
    time_steps = range(len(test_preds))
    
    # 绘制实际值和预测值（反标准化后）
    plt.plot(time_steps, inverse_targets[:, 0, i], label=f'真实值 PC{i+1}', color='blue')
    plt.plot(time_steps, inverse_preds[:, 0, i], label=f'预测值 PC{i+1}', color='red', alpha=0.7)
    
    plt.xlabel('时间步')
    plt.ylabel(f'PC{i+1}值')
    plt.title(f'模态{i+1}在测试集上的预测结果')
    plt.legend()
    plt.grid(True)
    plt.savefig(f'model_figures/test_predictions_pc{i+1}.png', dpi=300, bbox_inches='tight')

print("EOF时间系数预测训练完成！") 