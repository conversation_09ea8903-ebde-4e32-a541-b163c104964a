#!/usr/bin/env python3
"""
SST+PCs+ERA5数据预处理脚本
处理ERA5多变量数据的插值、标准化和与SST、PCs的对齐

作者: AI Assistant
日期: 2025-07-02
"""

import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import pandas as pd
from sklearn.preprocessing import StandardScaler
from scipy.interpolate import griddata
import os
from tqdm import tqdm

print("开始SST+PCs+ERA5数据预处理...")

# 创建结果文件夹
os.makedirs('sst_pcs_era5_data', exist_ok=True)

# 读取数据
print("读取SST和ERA5数据...")
sst_data = xr.open_dataset('SST-V2.nc')
era5_data = xr.open_dataset('data_ERA5.nc')

print(f"SST数据形状: {sst_data.analysed_sst.shape}")
print(f"ERA5数据形状: {era5_data.dims}")

# 重命名时间维度
era5_data = era5_data.rename({'valid_time': 'time'})

# 确保时间维度匹配
min_time_len = min(len(sst_data.time), len(era5_data.time))
print(f"匹配时间长度: {min_time_len}")

# 截取匹配的时间长度
sst_data_matched = sst_data.isel(time=slice(0, min_time_len))
era5_data_matched = era5_data.isel(time=slice(0, min_time_len))

def interpolate_era5_to_sst_grid(era5_ds, sst_ds, var_name):
    """将ERA5变量插值到SST网格"""
    print(f"插值 {var_name} 到SST网格...")
    
    # 创建原始经纬度网格点
    lon_era5, lat_era5 = np.meshgrid(era5_ds.longitude.values, era5_ds.latitude.values)
    
    # 创建目标经纬度网格点
    lon_sst, lat_sst = np.meshgrid(sst_ds.longitude.values, sst_ds.latitude.values)
    
    # 初始化结果数组
    result = np.zeros((len(era5_ds.time), len(sst_ds.latitude), len(sst_ds.longitude)))
    
    # 对每个时间点进行插值
    for t in tqdm(range(len(era5_ds.time)), desc=f"插值{var_name}"):
        # 获取ERA5当前时间的数据
        z = era5_ds[var_name].isel(time=t).values
        
        # 将原始经纬度网格和数据平铺
        points = np.column_stack((lon_era5.flatten(), lat_era5.flatten()))
        values = z.flatten()
        
        # 使用griddata进行插值
        result[t] = griddata(points, values, (lon_sst, lat_sst), method='linear')
    
    return result

# 插值所有ERA5变量到SST网格
print("插值所有ERA5变量到SST网格...")
era5_vars = ['u10', 'v10', 't2m', 'msl']
era5_interpolated = {}

for var in era5_vars:
    if var in era5_data_matched.data_vars:
        era5_interpolated[var] = interpolate_era5_to_sst_grid(era5_data_matched, sst_data_matched, var)

# 获取SST数据
sst_values = sst_data_matched.analysed_sst.values

print(f"SST数据形状: {sst_values.shape}")
for var, data in era5_interpolated.items():
    print(f"{var}数据形状: {data.shape}")

# 数据集划分（与EOF分解保持一致：7:2:1）
n_time_total = sst_values.shape[0]
train_end = int(n_time_total * 0.7)
val_end = int(n_time_total * 0.9)

print(f"数据集划分:")
print(f"  训练集: 0 - {train_end} ({train_end} 个时间点)")
print(f"  验证集: {train_end} - {val_end} ({val_end - train_end} 个时间点)")
print(f"  测试集: {val_end} - {n_time_total} ({n_time_total - val_end} 个时间点)")

# 划分数据集
sst_train = sst_values[:train_end]
sst_val = sst_values[train_end:val_end]
sst_test = sst_values[val_end:]

era5_train = {}
era5_val = {}
era5_test = {}

for var, data in era5_interpolated.items():
    era5_train[var] = data[:train_end]
    era5_val[var] = data[train_end:val_end]
    era5_test[var] = data[val_end:]

# 数据标准化
print("标准化数据...")

# 计算训练集的均值和标准差
sst_mean = np.mean(sst_train)
sst_std = np.std(sst_train)

era5_stats = {}
for var in era5_vars:
    if var in era5_interpolated:
        era5_stats[var] = {
            'mean': np.mean(era5_train[var]),
            'std': np.std(era5_train[var])
        }

print("标准化参数:")
print(f"SST: mean={sst_mean:.3f}, std={sst_std:.3f}")
for var, stats in era5_stats.items():
    print(f"{var}: mean={stats['mean']:.3f}, std={stats['std']:.3f}")

# 标准化函数
def normalize_data(data, mean, std):
    return (data - mean) / std

# 标准化所有数据
sst_train_norm = normalize_data(sst_train, sst_mean, sst_std)
sst_val_norm = normalize_data(sst_val, sst_mean, sst_std)
sst_test_norm = normalize_data(sst_test, sst_mean, sst_std)

era5_train_norm = {}
era5_val_norm = {}
era5_test_norm = {}

for var in era5_vars:
    if var in era5_interpolated:
        mean = era5_stats[var]['mean']
        std = era5_stats[var]['std']
        
        era5_train_norm[var] = normalize_data(era5_train[var], mean, std)
        era5_val_norm[var] = normalize_data(era5_val[var], mean, std)
        era5_test_norm[var] = normalize_data(era5_test[var], mean, std)

# 加载PCs数据
print("加载PCs数据...")
try:
    PCs_train = np.load('results/PCs_train.npy')  # 形状: (k, train_len)
    PCs_val = np.load('results/PCs_val.npy')      # 形状: (k, val_len)
    PCs_test = np.load('results/PCs_test.npy')    # 形状: (k, test_len)
    
    print(f"PCs训练集形状: {PCs_train.shape}")
    print(f"PCs验证集形状: {PCs_val.shape}")
    print(f"PCs测试集形状: {PCs_test.shape}")
    
    # 转置PCs数据以匹配时间维度
    PCs_train = PCs_train.T  # 形状: (train_len, k)
    PCs_val = PCs_val.T      # 形状: (val_len, k)
    PCs_test = PCs_test.T    # 形状: (test_len, k)
    
    # 标准化PCs数据
    pc_scaler = StandardScaler()
    PCs_train_scaled = pc_scaler.fit_transform(PCs_train)
    PCs_val_scaled = pc_scaler.transform(PCs_val)
    PCs_test_scaled = pc_scaler.transform(PCs_test)
    
    print("PCs数据标准化完成")
    
except FileNotFoundError:
    print("警告: 未找到PCs数据文件，请先运行 preprocess_and_eof.py")
    PCs_train_scaled = None
    PCs_val_scaled = None
    PCs_test_scaled = None
    pc_scaler = None

# 保存标准化参数
print("保存标准化参数...")
np.save('sst_pcs_era5_data/sst_mean.npy', sst_mean)
np.save('sst_pcs_era5_data/sst_std.npy', sst_std)

for var, stats in era5_stats.items():
    np.save(f'sst_pcs_era5_data/{var}_mean.npy', stats['mean'])
    np.save(f'sst_pcs_era5_data/{var}_std.npy', stats['std'])

if pc_scaler is not None:
    np.save('sst_pcs_era5_data/pc_scaler_mean.npy', pc_scaler.mean_)
    np.save('sst_pcs_era5_data/pc_scaler_scale.npy', pc_scaler.scale_)

# 保存标准化后的数据
print("保存标准化后的数据...")

# SST数据
np.save('sst_pcs_era5_data/sst_train_norm.npy', sst_train_norm)
np.save('sst_pcs_era5_data/sst_val_norm.npy', sst_val_norm)
np.save('sst_pcs_era5_data/sst_test_norm.npy', sst_test_norm)

# ERA5数据
for var in era5_vars:
    if var in era5_train_norm:
        np.save(f'sst_pcs_era5_data/{var}_train_norm.npy', era5_train_norm[var])
        np.save(f'sst_pcs_era5_data/{var}_val_norm.npy', era5_val_norm[var])
        np.save(f'sst_pcs_era5_data/{var}_test_norm.npy', era5_test_norm[var])

# PCs数据
if PCs_train_scaled is not None:
    np.save('sst_pcs_era5_data/pcs_train_scaled.npy', PCs_train_scaled)
    np.save('sst_pcs_era5_data/pcs_val_scaled.npy', PCs_val_scaled)
    np.save('sst_pcs_era5_data/pcs_test_scaled.npy', PCs_test_scaled)

print("SST+PCs+ERA5数据预处理完成！")
print("结果保存在 sst_pcs_era5_data/ 文件夹中")

# 关闭数据集
sst_data.close()
era5_data.close()
