# EOF加权空间映射ConvLSTM实验

## 🎯 实验目标

实现**策略2：EOF加权空间映射**，使用EOF模态的空间结构对主成分(PCs)进行加权映射，将加权后的空间场作为额外输入通道与SST和ERA5数据结合，输入到ConvLSTM模型中预测SST。

## 🔬 核心思想

### 传统方法的问题
- **PCs广播方法**: 将PCs值简单广播到整个空间，丢失了空间结构信息
- **EOF重构方法**: 需要预测PCs再重构，存在误差累积问题

### 策略2的创新
- **空间加权映射**: `加权场(t,k) = PC(t,k) × EOF空间模态(k)`
- **保持物理结构**: 每个EOF模态的空间模式得到保持
- **直接特征输入**: 加权场直接作为ConvLSTM的输入特征

## 📊 数据流程

```
原始数据:
├── SST历史序列: [seq_len, lat, lon]
├── ERA5大气变量: [seq_len, 4, lat, lon] 
└── PCs时间序列: [seq_len, k]

EOF加权映射:
PCs[t,k] × EOF_spatial[k] → 加权空间场[t,k,lat,lon]

模型输入:
[SST + ERA5 + EOF加权场] → ConvLSTM → 预测SST
[seq_len, 1+4+k, lat, lon] → [pred_len, lat, lon]
```

## 🏗️ 模型架构

### 输入特征 (总计15通道)
1. **SST通道** (1个): 海表温度历史序列
2. **ERA5通道** (4个): u10, v10, t2m, msl大气变量
3. **EOF加权通道** (10个): 10个EOF模态的加权空间场

### 网络结构
```
输入特征 → 特征预处理 → 通道注意力 → ConvLSTM → 输出投影 → 残差连接
```

#### 关键组件
- **EOF特征处理器**: 专门处理EOF加权特征的卷积层
- **通道注意力**: 自适应调整不同特征类型的权重
- **多尺度ConvLSTM**: 捕捉不同空间尺度的时空模式
- **残差连接**: 保持与输入SST的连接

## 📁 文件结构

```
eof_weighted_mapping_experiment/
├── preprocess_eof_weighted_data.py     # 数据预处理脚本
├── eof_weighted_convlstm_model.py      # 模型定义
├── train_eof_weighted_convlstm.py      # 训练脚本
├── visualize_eof_weighted_results.py   # 可视化脚本
├── test_eof_weighted_pipeline.py       # 测试脚本
├── README.md                           # 本文档
├── eof_weighted_data/                  # 预处理数据
├── eof_weighted_results/               # 训练结果
├── eof_weighted_figures/               # 训练图表
└── eof_weighted_visualization/         # 可视化结果
```

## 🚀 使用方法

### 1. 环境准备
确保已安装必要的依赖包：
```bash
pip install torch numpy matplotlib xarray scipy scikit-learn tqdm
```

### 2. 数据预处理
```bash
# 首先确保已运行主项目的EOF分解
cd ..
python preprocess_and_eof.py

# 然后运行EOF加权数据预处理
cd eof_weighted_mapping_experiment
python preprocess_eof_weighted_data.py
```

### 3. 测试流水线
```bash
python test_eof_weighted_pipeline.py
```

### 4. 训练模型
```bash
python train_eof_weighted_convlstm.py
```

### 5. 可视化结果
```bash
python visualize_eof_weighted_results.py
```

## 🔧 技术细节

### EOF加权映射实现
```python
def create_eof_weighted_features(pcs_sequence, eof_modes_sst):
    """
    创建EOF加权空间特征
    
    参数:
    - pcs_sequence: [k, time_steps] PCs时间序列
    - eof_modes_sst: [lat, lon, k] SST的EOF空间模态
    
    返回:
    - weighted_features: [time_steps, k, lat, lon] 加权空间特征
    """
    for t in range(time_steps):
        for mode_idx in range(k):
            pc_value = pcs_sequence[mode_idx, t]
            weighted_features[t, mode_idx] = pc_value * eof_modes_sst[:, :, mode_idx]
```

### 模型前向传播
```python
def forward(self, sst_sequence, era5_sequence, eof_weighted_sequence):
    # 1. 特征预处理
    eof_processed = self.eof_feature_processor(eof_weighted_sequence)
    
    # 2. 特征拼接
    combined_features = torch.cat([sst_sequence, era5_sequence, eof_processed], dim=2)
    
    # 3. 通道注意力
    attention_weights = self.channel_attention(combined_features)
    attended_features = combined_features * attention_weights
    
    # 4. ConvLSTM处理
    convlstm_output = self.convlstm(attended_features)
    
    # 5. 输出投影 + 残差连接
    predicted_sst = self.output_projection(convlstm_output) + residual
```

## 📈 预期优势

### 1. 物理意义保持
- **空间结构**: EOF模态的空间模式得到完整保持
- **物理约束**: 加权场符合海洋动力学的空间分布
- **模态解释**: 每个加权通道对应明确的物理模式

### 2. 特征表达能力
- **多尺度信息**: 不同EOF模态捕捉不同尺度的变化
- **时空耦合**: 结合时间演化(PCs)和空间结构(EOFs)
- **非线性学习**: ConvLSTM学习复杂的特征交互

### 3. 计算效率
- **直接输入**: 避免重构过程的计算开销
- **并行处理**: 所有特征可以并行处理
- **内存友好**: 相比完整重构更节省内存

## 🎯 实验假设

### 核心假设
1. **EOF空间结构包含重要信息**: EOF模态的空间分布对SST预测有价值
2. **加权映射优于简单广播**: 保持空间结构比均匀分布更有效
3. **多特征融合提升性能**: SST+ERA5+EOF的组合优于单一特征

### 验证方法
- **对比实验**: 与纯SST、SST+ERA5、SST+PCs模型对比
- **消融实验**: 验证EOF加权特征的独立贡献
- **可视化分析**: 检查预测结果的物理合理性

## 📊 评估指标

### 定量指标
- **RMSE**: 均方根误差 (主要指标)
- **MAE**: 平均绝对误差
- **空间相关性**: 预测场与真实场的空间相关系数

### 定性分析
- **空间模式**: 预测SST场的空间分布合理性
- **时间演化**: 预测序列的时间连续性
- **极值处理**: 对极端SST事件的预测能力

## 🔮 预期结果

### 性能预期
- **相比纯SST模型**: RMSE改善15-25%
- **相比SST+ERA5模型**: RMSE改善5-10%
- **相比SST+PCs模型**: 保持相似精度但物理意义更强

### 科学价值
- **方法创新**: 提出新的EOF特征利用方式
- **物理约束**: 在深度学习中融入海洋物理知识
- **可解释性**: 增强模型预测的可解释性

## 🚧 注意事项

### 数据要求
- 需要完整的EOF分解结果
- SST和ERA5数据需要时空对齐
- 确保数据标准化的一致性

### 计算资源
- GPU内存需求较高 (15通道输入)
- 建议使用8GB以上显存的GPU
- 可通过减少batch_size适应硬件限制

### 调试建议
- 先运行测试脚本验证流水线
- 检查EOF加权特征的数值范围
- 监控训练过程中的梯度变化

---

*本实验实现了EOF分解与深度学习结合的创新方法，为海表温度预测提供了新的技术路径。*
