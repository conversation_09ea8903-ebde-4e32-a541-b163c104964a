#!/usr/bin/env python3
"""
EOF加权空间映射ConvLSTM模型训练脚本
实现策略2：使用EOF模态的空间结构对PCs进行加权映射

作者: AI Assistant
日期: 2025-07-07
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False
import os
import time
from sklearn.metrics import mean_squared_error, mean_absolute_error
from tqdm import tqdm

# 导入模型
from eof_weighted_convlstm_model import EOFWeightedConvLSTMModel, MultiScaleEOFWeightedConvLSTMModel

print("🚀 开始EOF加权空间映射ConvLSTM模型训练...")

# 创建结果文件夹
os.makedirs('eof_weighted_results', exist_ok=True)
os.makedirs('eof_weighted_figures', exist_ok=True)

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 检查GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

# 加载预处理数据
print("📊 加载预处理数据...")

# 加载SST数据
sst_train = np.load('eof_weighted_data/sst_train_norm.npy')
sst_val = np.load('eof_weighted_data/sst_val_norm.npy')
sst_test = np.load('eof_weighted_data/sst_test_norm.npy')

# 加载ERA5数据
era5_vars = ['u10', 'v10', 't2m', 'msl']
era5_train = {}
era5_val = {}
era5_test = {}

for var in era5_vars:
    try:
        era5_train[var] = np.load(f'eof_weighted_data/{var}_train_norm.npy')
        era5_val[var] = np.load(f'eof_weighted_data/{var}_val_norm.npy')
        era5_test[var] = np.load(f'eof_weighted_data/{var}_test_norm.npy')
        print(f"✅ 加载 {var} 数据")
    except FileNotFoundError:
        print(f"❌ 未找到 {var} 数据文件")

# 加载EOF加权特征
eof_weighted_train = np.load('eof_weighted_data/train_eof_weighted_features.npy')
eof_weighted_val = np.load('eof_weighted_data/val_eof_weighted_features.npy')
eof_weighted_test = np.load('eof_weighted_data/test_eof_weighted_features.npy')

print(f"SST训练集形状: {sst_train.shape}")
print(f"EOF加权特征训练集形状: {eof_weighted_train.shape}")

# 组合ERA5数据
def combine_era5_data(era5_dict):
    """组合ERA5变量为单个数组"""
    era5_list = []
    for var in era5_vars:
        if var in era5_dict:
            era5_list.append(era5_dict[var])
    return np.stack(era5_list, axis=1)  # [time, vars, lat, lon]

era5_train_combined = combine_era5_data(era5_train)
era5_val_combined = combine_era5_data(era5_val)
era5_test_combined = combine_era5_data(era5_test)

print(f"ERA5训练集形状: {era5_train_combined.shape}")

class EOFWeightedDataset(Dataset):
    """EOF加权数据集类"""
    
    def __init__(self, sst_data, era5_data, eof_weighted_data, seq_len=14, pred_len=1):
        self.sst_data = sst_data
        self.era5_data = era5_data
        self.eof_weighted_data = eof_weighted_data
        self.seq_len = seq_len
        self.pred_len = pred_len
        
        # 计算有效样本数量
        self.n_samples = len(sst_data) - seq_len - pred_len + 1
        
        print(f"数据集初始化: {self.n_samples} 个样本")
        
    def __len__(self):
        return self.n_samples
    
    def __getitem__(self, idx):
        # SST输入序列和目标
        sst_input = self.sst_data[idx:idx+self.seq_len]  # [seq_len, lat, lon]
        sst_target = self.sst_data[idx+self.seq_len:idx+self.seq_len+self.pred_len]  # [pred_len, lat, lon]
        
        # ERA5输入序列
        era5_input = self.era5_data[idx:idx+self.seq_len]  # [seq_len, 4, lat, lon]
        
        # EOF加权特征输入序列
        eof_weighted_input = self.eof_weighted_data[idx:idx+self.seq_len]  # [seq_len, k, lat, lon]
        
        # 添加通道维度到SST
        sst_input = sst_input[:, np.newaxis, :, :]  # [seq_len, 1, lat, lon]
        
        return (torch.FloatTensor(sst_input), 
                torch.FloatTensor(era5_input), 
                torch.FloatTensor(eof_weighted_input),
                torch.FloatTensor(sst_target))

# 超参数设置
seq_len = 14
pred_len = 1
batch_size = 4  # 由于输入通道较多，减小批次大小
hidden_dim = 64
num_layers = 2
learning_rate = 0.001
weight_decay = 1e-4
num_epochs = 60
patience = 12
clip_grad = 1.0

print(f"\n⚙️ 超参数设置:")
print(f"  序列长度: {seq_len}")
print(f"  预测长度: {pred_len}")
print(f"  批次大小: {batch_size}")
print(f"  隐藏维度: {hidden_dim}")
print(f"  学习率: {learning_rate}")

# 创建数据集和数据加载器
print("\n📦 创建数据集和数据加载器...")

train_dataset = EOFWeightedDataset(sst_train, era5_train_combined, eof_weighted_train, seq_len, pred_len)
val_dataset = EOFWeightedDataset(sst_val, era5_val_combined, eof_weighted_val, seq_len, pred_len)
test_dataset = EOFWeightedDataset(sst_test, era5_test_combined, eof_weighted_test, seq_len, pred_len)

train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=0)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=0)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=0)

print(f"数据加载器创建完成:")
print(f"  训练批次数: {len(train_loader)}")
print(f"  验证批次数: {len(val_loader)}")
print(f"  测试批次数: {len(test_loader)}")

# 训练函数
def train_model(model, model_name):
    print(f"\n🎯 开始训练 {model_name} 模型...")
    
    # 优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    criterion = nn.MSELoss()
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)
    
    # 训练历史
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    print(f"开始训练，共 {num_epochs} 个epoch...")
    
    for epoch in range(num_epochs):
        start_time = time.time()
        
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Train]')
        
        for batch_idx, (sst_input, era5_input, eof_input, sst_target) in enumerate(train_pbar):
            sst_input = sst_input.to(device)
            era5_input = era5_input.to(device)
            eof_input = eof_input.to(device)
            sst_target = sst_target.to(device)
            
            optimizer.zero_grad()
            outputs = model(sst_input, era5_input, eof_input)
            loss = criterion(outputs, sst_target)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), clip_grad)
            
            optimizer.step()
            train_loss += loss.item()
            
            train_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f'Epoch {epoch+1}/{num_epochs} [Val]')
            for sst_input, era5_input, eof_input, sst_target in val_pbar:
                sst_input = sst_input.to(device)
                era5_input = era5_input.to(device)
                eof_input = eof_input.to(device)
                sst_target = sst_target.to(device)
                
                outputs = model(sst_input, era5_input, eof_input)
                loss = criterion(outputs, sst_target)
                val_loss += loss.item()
                val_pbar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        avg_val_loss = val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        
        # 学习率调度
        scheduler.step(avg_val_loss)
        
        epoch_time = time.time() - start_time
        print(f'Epoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s):')
        print(f'  训练损失: {avg_train_loss:.6f}')
        print(f'  验证损失: {avg_val_loss:.6f}')
        
        # 早停检查
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), f'eof_weighted_results/best_{model_name}.pth')
            print(f'  💾 保存最佳模型 (验证损失: {best_val_loss:.6f})')
        else:
            patience_counter += 1
            print(f'  ⏳ 早停计数: {patience_counter}/{patience}')
        
        if patience_counter >= patience:
            print(f'🛑 早停触发，停止训练')
            break
    
    return train_losses, val_losses

# 测试模型性能
def test_model(model, model_name):
    print(f"\n🧪 测试 {model_name} 模型性能...")
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'eof_weighted_results/best_{model_name}.pth'))
    model.eval()
    
    test_predictions = []
    test_targets = []
    
    with torch.no_grad():
        test_pbar = tqdm(test_loader, desc=f'测试 {model_name}')
        for sst_input, era5_input, eof_input, sst_target in test_pbar:
            sst_input = sst_input.to(device)
            era5_input = era5_input.to(device)
            eof_input = eof_input.to(device)
            sst_target = sst_target.to(device)
            
            outputs = model(sst_input, era5_input, eof_input)
            
            test_predictions.append(outputs.cpu().numpy())
            test_targets.append(sst_target.cpu().numpy())
    
    # 合并所有预测结果
    test_predictions = np.concatenate(test_predictions, axis=0)
    test_targets = np.concatenate(test_targets, axis=0)
    
    # 计算评估指标
    mse = mean_squared_error(test_targets.flatten(), test_predictions.flatten())
    mae = mean_absolute_error(test_targets.flatten(), test_predictions.flatten())
    rmse = np.sqrt(mse)
    
    print(f"{model_name} EOF加权模型测试结果:")
    print(f"  MSE: {mse:.6f}")
    print(f"  MAE: {mae:.6f}")
    print(f"  RMSE: {rmse:.6f}")
    
    # 保存测试结果
    np.save(f'eof_weighted_results/test_predictions_{model_name}.npy', test_predictions)
    np.save(f'eof_weighted_results/test_targets_{model_name}.npy', test_targets)
    
    return test_predictions, test_targets, {'mse': mse, 'mae': mae, 'rmse': rmse}

# 绘制训练曲线
def plot_training_curves(train_losses, val_losses, model_name):
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='训练损失', color='blue')
    plt.plot(val_losses, label='验证损失', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.title(f'{model_name} EOF加权模型训练曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'eof_weighted_figures/training_curves_{model_name}.png', dpi=300, bbox_inches='tight')
    plt.close()

# 选择要训练的模型类型
model_type = "basic"  # 可选: "basic", "multiscale", "both"

if model_type in ["basic", "both"]:
    print("\n" + "="*60)
    print("训练基础EOF加权ConvLSTM模型")
    print("="*60)
    
    # 创建基础模型
    basic_model = EOFWeightedConvLSTMModel(
        num_eof_modes=eof_weighted_train.shape[1],  # 使用实际的EOF模态数量
        seq_len=seq_len,
        pred_len=pred_len,
        hidden_dim=hidden_dim,
        num_layers=num_layers
    ).to(device)
    
    print(f"基础模型参数数量: {sum(p.numel() for p in basic_model.parameters()):,}")
    
    # 训练模型
    train_losses, val_losses = train_model(basic_model, "eof_weighted_basic")
    
    # 测试模型
    test_predictions, test_targets, test_metrics = test_model(basic_model, "eof_weighted_basic")
    plot_training_curves(train_losses, val_losses, "eof_weighted_basic")

print("\n🎉 EOF加权空间映射ConvLSTM模型训练完成！")
print("这是策略2的实现：使用EOF模态的空间结构对PCs进行加权映射")
